import 'package:flutter/material.dart';
import '../models/specialist.dart';
import '../utils/constants.dart';
import '../utils/arabic_text.dart';

class SpecialistCard extends StatelessWidget {
  final Specialist specialist;
  final VoidCallback onCall;
  final VoidCallback onEmail;
  final VoidCallback onBookAppointment;

  const SpecialistCard({
    super.key,
    required this.specialist,
    required this.onCall,
    required this.onEmail,
    required this.onBookAppointment,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with photo and basic info
              _buildHeader(),
              
              const SizedBox(height: AppConstants.paddingMedium),
              
              // Specialization and experience
              _buildSpecializationInfo(),
              
              const SizedBox(height: AppConstants.paddingMedium),
              
              // Contact info
              _buildContactInfo(),
              
              const SizedBox(height: AppConstants.paddingMedium),
              
              // Working hours and availability
              _buildAvailabilityInfo(),
              
              const SizedBox(height: AppConstants.paddingLarge),
              
              // Action buttons
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        // Profile photo placeholder
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: AppConstants.primaryGradient,
            boxShadow: [
              BoxShadow(
                color: AppConstants.primaryColor.withOpacity(0.3),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: const Icon(
            Icons.person,
            color: Colors.white,
            size: 30,
          ),
        ),
        
        const SizedBox(width: AppConstants.paddingMedium),
        
        // Name and rating
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                specialist.fullName,
                style: AppConstants.subHeadingStyle,
              ),
              const SizedBox(height: AppConstants.paddingSmall),
              Row(
                children: [
                  _buildRatingStars(),
                  const SizedBox(width: AppConstants.paddingSmall),
                  Text(
                    '(${specialist.rating})',
                    style: AppConstants.captionStyle,
                  ),
                ],
              ),
            ],
          ),
        ),
        
        // Availability indicator
        _buildAvailabilityIndicator(),
      ],
    );
  }

  Widget _buildRatingStars() {
    return Row(
      children: List.generate(5, (index) {
        return Icon(
          index < specialist.rating.floor()
              ? Icons.star
              : (index < specialist.rating ? Icons.star_half : Icons.star_border),
          color: AppConstants.warningColor,
          size: 16,
        );
      }),
    );
  }

  Widget _buildAvailabilityIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: specialist.isAvailable 
            ? AppConstants.successColor.withOpacity(0.1)
            : AppConstants.errorColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
        border: Border.all(
          color: specialist.isAvailable 
              ? AppConstants.successColor
              : AppConstants.errorColor,
        ),
      ),
      child: Text(
        specialist.isAvailable ? ArabicText.available : ArabicText.notAvailable,
        style: AppConstants.captionStyle.copyWith(
          color: specialist.isAvailable 
              ? AppConstants.successColor
              : AppConstants.errorColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildSpecializationInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.medical_services,
              color: AppConstants.primaryColor,
              size: 20,
            ),
            const SizedBox(width: AppConstants.paddingSmall),
            Expanded(
              child: Text(
                specialist.specialization,
                style: AppConstants.bodyStyle.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Row(
          children: [
            Icon(
              Icons.work_history,
              color: AppConstants.textSecondaryColor,
              size: 20,
            ),
            const SizedBox(width: AppConstants.paddingSmall),
            Text(
              '${ArabicText.experience}: ${specialist.experienceYears} سنوات',
              style: AppConstants.captionStyle,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildContactInfo() {
    return Column(
      children: [
        _buildInfoRow(Icons.location_on, specialist.clinicAddress),
        const SizedBox(height: AppConstants.paddingSmall),
        _buildInfoRow(Icons.phone, specialist.phoneNumber),
        const SizedBox(height: AppConstants.paddingSmall),
        _buildInfoRow(Icons.email, specialist.email),
      ],
    );
  }

  Widget _buildInfoRow(IconData icon, String text) {
    return Row(
      children: [
        Icon(
          icon,
          color: AppConstants.textSecondaryColor,
          size: 18,
        ),
        const SizedBox(width: AppConstants.paddingSmall),
        Expanded(
          child: Text(
            text,
            style: AppConstants.captionStyle,
          ),
        ),
      ],
    );
  }

  Widget _buildAvailabilityInfo() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.backgroundColor,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.access_time,
                color: AppConstants.primaryColor,
                size: 20,
              ),
              const SizedBox(width: AppConstants.paddingSmall),
              Text(
                ArabicText.workingHours,
                style: AppConstants.bodyStyle.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            specialist.workingHours,
            style: AppConstants.captionStyle,
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Wrap(
            spacing: AppConstants.paddingSmall,
            children: specialist.languages.map((language) => 
              Chip(
                label: Text(
                  language,
                  style: const TextStyle(fontSize: 12),
                ),
                backgroundColor: AppConstants.primaryColor.withOpacity(0.1),
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        // Call button
        Expanded(
          child: ElevatedButton.icon(
            onPressed: onCall,
            icon: const Icon(Icons.phone, size: 18),
            label: const Text(ArabicText.callNow),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.successColor,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        
        const SizedBox(width: AppConstants.paddingSmall),
        
        // Email button
        Expanded(
          child: ElevatedButton.icon(
            onPressed: onEmail,
            icon: const Icon(Icons.email, size: 18),
            label: const Text(ArabicText.sendEmail),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.secondaryColor,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        
        const SizedBox(width: AppConstants.paddingSmall),
        
        // Book appointment button
        Expanded(
          child: ElevatedButton.icon(
            onPressed: specialist.isAvailable ? onBookAppointment : null,
            icon: const Icon(Icons.calendar_today, size: 18),
            label: const Text(ArabicText.bookAppointment),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryColor,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }
}
