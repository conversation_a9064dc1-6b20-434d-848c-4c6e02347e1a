import 'package:flutter/material.dart';
import '../utils/constants.dart';
import '../utils/arabic_text.dart';

class PronunciationFeedback extends StatefulWidget {
  final bool isCorrect;
  final int attempts;
  final double? accuracyScore;

  const PronunciationFeedback({
    super.key,
    required this.isCorrect,
    required this.attempts,
    this.accuracyScore,
  });

  @override
  State<PronunciationFeedback> createState() => _PronunciationFeedbackState();
}

class _PronunciationFeedbackState extends State<PronunciationFeedback>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _colorController;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;

  @override
  void initState() {
    super.initState();
    
    _scaleController = AnimationController(
      duration: AppConstants.animationMedium,
      vsync: this,
    );
    
    _colorController = AnimationController(
      duration: AppConstants.animationSlow,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _colorAnimation = ColorTween(
      begin: Colors.transparent,
      end: widget.isCorrect ? AppConstants.successColor : AppConstants.errorColor,
    ).animate(CurvedAnimation(
      parent: _colorController,
      curve: Curves.easeInOut,
    ));

    _startAnimations();
  }

  void _startAnimations() {
    _scaleController.forward();
    _colorController.forward();
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _colorController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_scaleAnimation, _colorAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            padding: const EdgeInsets.all(AppConstants.paddingLarge),
            decoration: BoxDecoration(
              color: _colorAnimation.value?.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
              border: Border.all(
                color: _colorAnimation.value ?? Colors.transparent,
                width: 2,
              ),
            ),
            child: Column(
              children: [
                // Feedback icon
                _buildFeedbackIcon(),
                
                const SizedBox(height: AppConstants.paddingMedium),
                
                // Feedback text
                _buildFeedbackText(),
                
                const SizedBox(height: AppConstants.paddingMedium),
                
                // Accuracy score if available
                if (widget.accuracyScore != null) _buildAccuracyScore(),
                
                // Encouragement message
                _buildEncouragementMessage(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFeedbackIcon() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: widget.isCorrect ? AppConstants.successColor : AppConstants.errorColor,
        boxShadow: [
          BoxShadow(
            color: (widget.isCorrect ? AppConstants.successColor : AppConstants.errorColor)
                .withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Icon(
        widget.isCorrect ? Icons.check : Icons.close,
        size: 40,
        color: Colors.white,
      ),
    );
  }

  Widget _buildFeedbackText() {
    return Text(
      widget.isCorrect ? ArabicText.correctPronunciation : ArabicText.incorrectPronunciation,
      style: AppConstants.subHeadingStyle.copyWith(
        color: widget.isCorrect ? AppConstants.successColor : AppConstants.errorColor,
        fontWeight: FontWeight.bold,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildAccuracyScore() {
    return Column(
      children: [
        Text(
          'دقة النطق',
          style: AppConstants.captionStyle,
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Container(
          width: 200,
          height: 8,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(4),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: (widget.accuracyScore ?? 0) / 100,
            child: Container(
              decoration: BoxDecoration(
                color: _getAccuracyColor(widget.accuracyScore ?? 0),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Text(
          '${(widget.accuracyScore ?? 0).toStringAsFixed(1)}%',
          style: AppConstants.bodyStyle.copyWith(
            fontWeight: FontWeight.bold,
            color: _getAccuracyColor(widget.accuracyScore ?? 0),
          ),
        ),
      ],
    );
  }

  Widget _buildEncouragementMessage() {
    String message = _getEncouragementMessage();
    
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.8),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Text(
        message,
        style: AppConstants.bodyStyle.copyWith(
          fontStyle: FontStyle.italic,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  String _getEncouragementMessage() {
    if (widget.isCorrect) {
      if (widget.attempts == 1) {
        return 'ممتاز! نطق صحيح من المحاولة الأولى! 🌟';
      } else if (widget.attempts <= 3) {
        return 'أحسنت! تحسن رائع في النطق! 👏';
      } else {
        return 'رائع! المثابرة تؤتي ثمارها! 🎉';
      }
    } else {
      if (widget.attempts == 1) {
        return 'لا بأس، حاول مرة أخرى بهدوء 😊';
      } else if (widget.attempts <= 3) {
        return 'استمر في المحاولة، أنت تتحسن! 💪';
      } else {
        return 'لا تستسلم، كل محاولة تقربك من الهدف! 🌈';
      }
    }
  }

  Color _getAccuracyColor(double score) {
    if (score >= 80) {
      return AppConstants.successColor;
    } else if (score >= 60) {
      return AppConstants.warningColor;
    } else {
      return AppConstants.errorColor;
    }
  }
}
