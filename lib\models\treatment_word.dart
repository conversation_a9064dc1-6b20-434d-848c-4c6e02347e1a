import 'package:flutter/material.dart';

class TreatmentWord {
  final String word;
  final String emoji;
  final IconData icon;
  final String description;
  final String audioPath;
  final Color color;

  TreatmentWord({
    required this.word,
    required this.emoji,
    required this.icon,
    required this.description,
    required this.audioPath,
    required this.color,
  });

  static List<TreatmentWord> getWordsForStuttering() {
    return [
      TreatmentWord(
        word: 'بابا',
        emoji: '👨',
        icon: Icons.person,
        description: 'والد',
        audioPath: 'assets/audio/words/baba.mp3',
        color: const Color(0xFF2196F3),
      ),
      TreatmentWord(
        word: 'ماما',
        emoji: '👩',
        icon: Icons.person,
        description: 'والدة',
        audioPath: 'assets/audio/words/mama.mp3',
        color: const Color(0xFFE91E63),
      ),
      TreatmentWord(
        word: 'بيت',
        emoji: '🏠',
        icon: Icons.home,
        description: 'منزل',
        audioPath: 'assets/audio/words/bayt.mp3',
        color: const Color(0xFF4CAF50),
      ),
      TreatmentWord(
        word: 'كتاب',
        emoji: '📚',
        icon: Icons.book,
        description: 'للقراءة',
        audioPath: 'assets/audio/words/kitab.mp3',
        color: const Color(0xFF9C27B0),
      ),
      TreatmentWord(
        word: 'قلم',
        emoji: '✏️',
        icon: Icons.edit,
        description: 'للكتابة',
        audioPath: 'assets/audio/words/qalam.mp3',
        color: const Color(0xFFFF9800),
      ),
      TreatmentWord(
        word: 'شمس',
        emoji: '☀️',
        icon: Icons.wb_sunny,
        description: 'نجم النهار',
        audioPath: 'assets/audio/words/shams.mp3',
        color: const Color(0xFFFFC107),
      ),
      TreatmentWord(
        word: 'قمر',
        emoji: '🌙',
        icon: Icons.nightlight_round,
        description: 'نور الليل',
        audioPath: 'assets/audio/words/qamar.mp3',
        color: const Color(0xFF607D8B),
      ),
      TreatmentWord(
        word: 'ماء',
        emoji: '💧',
        icon: Icons.water_drop,
        description: 'للشرب',
        audioPath: 'assets/audio/words/maa.mp3',
        color: const Color(0xFF03A9F4),
      ),
      TreatmentWord(
        word: 'نار',
        emoji: '🔥',
        icon: Icons.local_fire_department,
        description: 'حارة',
        audioPath: 'assets/audio/words/nar.mp3',
        color: const Color(0xFFF44336),
      ),
      TreatmentWord(
        word: 'ورد',
        emoji: '🌹',
        icon: Icons.local_florist,
        description: 'زهرة جميلة',
        audioPath: 'assets/audio/words/ward.mp3',
        color: const Color(0xFFE91E63),
      ),
    ];
  }

  static List<TreatmentWord> getWordsForLisp() {
    return [
      TreatmentWord(
        word: 'سمك',
        emoji: '🐟',
        icon: Icons.set_meal,
        description: 'يعيش في الماء',
        audioPath: 'assets/audio/words/samak.mp3',
        color: const Color(0xFF00BCD4),
      ),
      TreatmentWord(
        word: 'شمس',
        emoji: '☀️',
        icon: Icons.wb_sunny,
        description: 'نجم النهار',
        audioPath: 'assets/audio/words/shams.mp3',
        color: const Color(0xFFFFC107),
      ),
      TreatmentWord(
        word: 'صباح',
        emoji: '🌅',
        icon: Icons.wb_twilight,
        description: 'بداية اليوم',
        audioPath: 'assets/audio/words/sabah.mp3',
        color: const Color(0xFFFF9800),
      ),
      TreatmentWord(
        word: 'زهرة',
        emoji: '🌸',
        icon: Icons.local_florist,
        description: 'جميلة ومعطرة',
        audioPath: 'assets/audio/words/zahra.mp3',
        color: const Color(0xFFE91E63),
      ),
      TreatmentWord(
        word: 'سيارة',
        emoji: '🚗',
        icon: Icons.directions_car,
        description: 'وسيلة نقل',
        audioPath: 'assets/audio/words/sayara.mp3',
        color: const Color(0xFF2196F3),
      ),
      TreatmentWord(
        word: 'صوت',
        emoji: '🔊',
        icon: Icons.volume_up,
        description: 'نسمعه بالأذن',
        audioPath: 'assets/audio/words/sawt.mp3',
        color: const Color(0xFF9C27B0),
      ),
      TreatmentWord(
        word: 'زيت',
        emoji: '🫒',
        icon: Icons.opacity,
        description: 'للطبخ',
        audioPath: 'assets/audio/words/zayt.mp3',
        color: const Color(0xFF4CAF50),
      ),
      TreatmentWord(
        word: 'سكر',
        emoji: '🍯',
        icon: Icons.cake,
        description: 'حلو المذاق',
        audioPath: 'assets/audio/words/sukkar.mp3',
        color: const Color(0xFFFFC107),
      ),
      TreatmentWord(
        word: 'صورة',
        emoji: '📷',
        icon: Icons.photo_camera,
        description: 'ذكرى جميلة',
        audioPath: 'assets/audio/words/sura.mp3',
        color: const Color(0xFF607D8B),
      ),
      TreatmentWord(
        word: 'زرافة',
        emoji: '🦒',
        icon: Icons.pets,
        description: 'حيوان طويل',
        audioPath: 'assets/audio/words/zarafa.mp3',
        color: const Color(0xFFFF9800),
      ),
    ];
  }

  static List<TreatmentWord> getWordsForSelectiveMutism() {
    return [
      TreatmentWord(
        word: 'مرحبا',
        emoji: '👋',
        icon: Icons.waving_hand,
        description: 'تحية',
        audioPath: 'assets/audio/words/marhaba.mp3',
        color: const Color(0xFF4CAF50),
      ),
      TreatmentWord(
        word: 'شكراً',
        emoji: '🙏',
        icon: Icons.favorite,
        description: 'كلمة امتنان',
        audioPath: 'assets/audio/words/shukran.mp3',
        color: const Color(0xFFE91E63),
      ),
      TreatmentWord(
        word: 'نعم',
        emoji: '✅',
        icon: Icons.check_circle,
        description: 'موافقة',
        audioPath: 'assets/audio/words/naam.mp3',
        color: const Color(0xFF4CAF50),
      ),
      TreatmentWord(
        word: 'لا',
        emoji: '❌',
        icon: Icons.cancel,
        description: 'رفض',
        audioPath: 'assets/audio/words/la.mp3',
        color: const Color(0xFFF44336),
      ),
      TreatmentWord(
        word: 'أريد',
        emoji: '🙋',
        icon: Icons.pan_tool,
        description: 'طلب',
        audioPath: 'assets/audio/words/ureed.mp3',
        color: const Color(0xFF2196F3),
      ),
      TreatmentWord(
        word: 'أحب',
        emoji: '❤️',
        icon: Icons.favorite,
        description: 'مشاعر إيجابية',
        audioPath: 'assets/audio/words/uhibb.mp3',
        color: const Color(0xFFE91E63),
      ),
      TreatmentWord(
        word: 'اسمي',
        emoji: '👤',
        icon: Icons.person,
        description: 'تعريف بالنفس',
        audioPath: 'assets/audio/words/ismi.mp3',
        color: const Color(0xFF9C27B0),
      ),
      TreatmentWord(
        word: 'صديق',
        emoji: '👫',
        icon: Icons.people,
        description: 'رفيق',
        audioPath: 'assets/audio/words/sadeeq.mp3',
        color: const Color(0xFF00BCD4),
      ),
      TreatmentWord(
        word: 'معلم',
        emoji: '👨‍🏫',
        icon: Icons.school,
        description: 'يعلم الطلاب',
        audioPath: 'assets/audio/words/muallim.mp3',
        color: const Color(0xFF607D8B),
      ),
      TreatmentWord(
        word: 'مدرسة',
        emoji: '🏫',
        icon: Icons.school,
        description: 'مكان التعلم',
        audioPath: 'assets/audio/words/madrasa.mp3',
        color: const Color(0xFF4CAF50),
      ),
    ];
  }

  static List<TreatmentWord> getWordsForDisorder(String disorderId) {
    switch (disorderId) {
      case 'stuttering':
        return getWordsForStuttering();
      case 'lisp':
        return getWordsForLisp();
      case 'selective_mutism':
        return getWordsForSelectiveMutism();
      default:
        return getWordsForStuttering();
    }
  }
}
