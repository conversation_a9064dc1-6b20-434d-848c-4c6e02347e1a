import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';

class AuthService extends ChangeNotifier {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  User? _currentUser;
  bool _isLoading = false;
  String? _errorMessage;

  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isLoggedIn => _currentUser != null;

  Future<void> initialize() async {
    _setLoading(true);
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString('current_user');
      if (userJson != null) {
        final userData = jsonDecode(userJson);
        _currentUser = User.fromJson(userData);
      }
    } catch (e) {
      _setError('خطأ في تحميل بيانات المستخدم');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> login(String email, String password) async {
    _setLoading(true);
    _clearError();

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 2));

      // In a real app, you would make an API call here
      // For demo purposes, we'll simulate a successful login
      if (email.isNotEmpty && password.length >= 6) {
        _currentUser = _createDemoUser(email);
        await _saveUserToStorage(_currentUser!);
        _setLoading(false);
        return true;
      } else {
        _setError('البريد الإلكتروني أو كلمة المرور غير صحيحة');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('خطأ في تسجيل الدخول. حاول مرة أخرى.');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> register({
    required String name,
    required String email,
    required String password,
    required DateTime dateOfBirth,
    required Gender gender,
    String? phoneNumber,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 2));

      // In a real app, you would make an API call here
      // For demo purposes, we'll simulate a successful registration
      if (name.isNotEmpty && email.isNotEmpty && password.length >= 6) {
        _currentUser = User(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: name,
          email: email,
          phoneNumber: phoneNumber,
          dateOfBirth: dateOfBirth,
          gender: gender,
          createdAt: DateTime.now(),
          progress: UserProgress.empty(),
          preferences: UserPreferences.defaultPreferences(),
        );
        
        await _saveUserToStorage(_currentUser!);
        _setLoading(false);
        return true;
      } else {
        _setError('يرجى التأكد من صحة البيانات المدخلة');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('خطأ في إنشاء الحساب. حاول مرة أخرى.');
      _setLoading(false);
      return false;
    }
  }

  Future<void> logout() async {
    _setLoading(true);
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_user');
      _currentUser = null;
    } catch (e) {
      _setError('خطأ في تسجيل الخروج');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateProfile({
    String? name,
    String? phoneNumber,
    DateTime? dateOfBirth,
    Gender? gender,
    String? profileImagePath,
  }) async {
    if (_currentUser == null) return false;

    _setLoading(true);
    _clearError();

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 1));

      _currentUser = _currentUser!.copyWith(
        name: name,
        phoneNumber: phoneNumber,
        dateOfBirth: dateOfBirth,
        gender: gender,
        profileImagePath: profileImagePath,
      );

      await _saveUserToStorage(_currentUser!);
      _setLoading(false);
      return true;
    } catch (e) {
      _setError('خطأ في تحديث الملف الشخصي');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> updatePreferences(UserPreferences preferences) async {
    if (_currentUser == null) return false;

    _setLoading(true);
    _clearError();

    try {
      _currentUser = _currentUser!.copyWith(preferences: preferences);
      await _saveUserToStorage(_currentUser!);
      _setLoading(false);
      return true;
    } catch (e) {
      _setError('خطأ في تحديث الإعدادات');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> updateProgress(UserProgress progress) async {
    if (_currentUser == null) return false;

    try {
      _currentUser = _currentUser!.copyWith(progress: progress);
      await _saveUserToStorage(_currentUser!);
      return true;
    } catch (e) {
      _setError('خطأ في تحديث التقدم');
      return false;
    }
  }

  Future<bool> resetPassword(String email) async {
    _setLoading(true);
    _clearError();

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 2));

      // In a real app, you would send a password reset email
      _setLoading(false);
      return true;
    } catch (e) {
      _setError('خطأ في إرسال رابط إعادة تعيين كلمة المرور');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> changePassword(String currentPassword, String newPassword) async {
    if (_currentUser == null) return false;

    _setLoading(true);
    _clearError();

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 1));

      // In a real app, you would verify the current password and update it
      _setLoading(false);
      return true;
    } catch (e) {
      _setError('خطأ في تغيير كلمة المرور');
      _setLoading(false);
      return false;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  Future<void> _saveUserToStorage(User user) async {
    final prefs = await SharedPreferences.getInstance();
    final userJson = jsonEncode(user.toJson());
    await prefs.setString('current_user', userJson);
  }

  User _createDemoUser(String email) {
    return User(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: 'مستخدم تجريبي',
      email: email,
      dateOfBirth: DateTime(2015, 1, 1), // 8 years old
      gender: Gender.male,
      createdAt: DateTime.now(),
      progress: UserProgress(
        totalSessions: 5,
        completedExercises: 12,
        streakDays: 3,
        lastSessionDate: DateTime.now().subtract(const Duration(days: 1)),
        skillLevels: {
          'pronunciation': 2,
          'listening': 3,
          'reading': 1,
        },
        completedLessons: ['fatiha', 'ikhlas'],
        pronunciationScores: {
          'fatiha': 85.5,
          'ikhlas': 92.0,
        },
      ),
      preferences: UserPreferences.defaultPreferences(),
    );
  }
}
