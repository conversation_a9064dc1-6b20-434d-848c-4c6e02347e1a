import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../models/specialist.dart';
import '../utils/constants.dart';
import '../utils/arabic_text.dart';
import '../widgets/specialist_card.dart';

class SpecialistsScreen extends StatefulWidget {
  const SpecialistsScreen({super.key});

  @override
  State<SpecialistsScreen> createState() => _SpecialistsScreenState();
}

class _SpecialistsScreenState extends State<SpecialistsScreen> {
  List<Specialist> _specialists = [];
  List<Specialist> _filteredSpecialists = [];
  String _searchQuery = '';
  String _selectedCity = 'الكل';

  @override
  void initState() {
    super.initState();
    _specialists = Specialist.getAllSpecialists();
    _filteredSpecialists = _specialists;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(ArabicText.specialistsTitle),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppConstants.backgroundGradient,
        ),
        child: Column(
          children: [
            // Search and filter section
            _buildSearchSection(),
            
            // Specialists list
            Expanded(
              child: _buildSpecialistsList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        children: [
          // Search bar
          TextField(
            decoration: InputDecoration(
              hintText: '🔍 ابحث عن أخصائي...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
              ),
              filled: true,
              fillColor: const Color.fromARGB(255, 244, 244, 244),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
                _filterSpecialists();
              });
            },
          ),
          
          const SizedBox(height: AppConstants.paddingMedium),
          
          // City filter
          Row(
            children: [
              Text(
                '🏙️ المدينة: ',
                style: AppConstants.bodyStyle,
              ),
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedCity,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: AppConstants.paddingMedium,
                      vertical: AppConstants.paddingSmall,
                    ),
                  ),
                  items: _getCities().map((city) {
                    return DropdownMenuItem(
                      value: city,
                      child: Text(city),
                    );
                  }).toList(),
                  onChanged: (city) {
                    setState(() {
                      _selectedCity = city ?? 'الكل';
                      _filterSpecialists();
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSpecialistsList() {
    if (_filteredSpecialists.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: AppConstants.textSecondaryColor,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              '😔 لم يتم العثور على أخصائيين',
              style: AppConstants.subHeadingStyle.copyWith(
                color: AppConstants.textSecondaryColor,
              ),
            ),
          ],
        ),
      );
    }

    return AnimationLimiter(
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        itemCount: _filteredSpecialists.length,
        itemBuilder: (context, index) {
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: AppConstants.animationMedium,
            child: SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(
                child: SpecialistCard(
                  specialist: _filteredSpecialists[index],
                  onCall: () => _makePhoneCall(_filteredSpecialists[index].phoneNumber),
                  onEmail: () => _sendEmail(_filteredSpecialists[index].email),
                  onBookAppointment: () => _bookAppointment(_filteredSpecialists[index]),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  List<String> _getCities() {
    final cities = ['الكل'];
    cities.addAll(_specialists.map((s) => s.location).toSet().toList());
    return cities;
  }

  void _filterSpecialists() {
    setState(() {
      _filteredSpecialists = _specialists.where((specialist) {
        final matchesSearch = _searchQuery.isEmpty ||
            specialist.fullName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            specialist.specialization.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            specialist.location.toLowerCase().contains(_searchQuery.toLowerCase());

        final matchesCity = _selectedCity == 'الكل' || specialist.location == _selectedCity;

        return matchesSearch && matchesCity;
      }).toList();
    });
  }

  void _makePhoneCall(String phoneNumber) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    } else {
      _showErrorSnackBar('لا يمكن إجراء المكالمة');
    }
  }

  void _sendEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
      query: 'subject=استفسار عن جلسات علاج النطق',
    );
    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    } else {
      _showErrorSnackBar('لا يمكن إرسال الإيميل');
    }
  }

  void _bookAppointment(Specialist specialist) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildAppointmentBottomSheet(specialist),
    );
  }

  Widget _buildAppointmentBottomSheet(Specialist specialist) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppConstants.radiusLarge),
          topRight: Radius.circular(AppConstants.radiusLarge),
        ),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: AppConstants.paddingMedium),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.all(AppConstants.paddingLarge),
            child: Column(
              children: [
                Text(
                  ArabicText.bookAppointment,
                  style: AppConstants.headingStyle,
                ),
                const SizedBox(height: AppConstants.paddingSmall),
                Text(
                  specialist.fullName,
                  style: AppConstants.subHeadingStyle.copyWith(
                    color: AppConstants.primaryColor,
                  ),
                ),
              ],
            ),
          ),
          
          // Appointment form
          Expanded(
            child: _buildAppointmentForm(specialist),
          ),
        ],
      ),
    );
  }

  Widget _buildAppointmentForm(Specialist specialist) {
    final nameController = TextEditingController();
    final phoneController = TextEditingController();
    final notesController = TextEditingController();
    DateTime selectedDate = DateTime.now().add(const Duration(days: 1));

    return Padding(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        children: [
          // Patient name
          TextField(
            controller: nameController,
            decoration: const InputDecoration(
              labelText: '👤 اسم المريض',
              border: OutlineInputBorder(),
            ),
          ),
          
          const SizedBox(height: AppConstants.paddingMedium),
          
          // Phone number
          TextField(
            controller: phoneController,
            decoration: const InputDecoration(
              labelText: '📱 رقم الهاتف',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.phone,
          ),
          
          const SizedBox(height: AppConstants.paddingMedium),
          
          // Date selection
          InkWell(
            onTap: () async {
              final date = await showDatePicker(
                context: context,
                initialDate: selectedDate,
                firstDate: DateTime.now(),
                lastDate: DateTime.now().add(const Duration(days: 30)),
              );
              if (date != null) {
                selectedDate = date;
              }
            },
            child: Container(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
              ),
              child: Row(
                children: [
                  const Icon(Icons.calendar_today),
                  const SizedBox(width: AppConstants.paddingMedium),
                  Text('📅 التاريخ المفضل: ${selectedDate.day}/${selectedDate.month}/${selectedDate.year}'),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: AppConstants.paddingMedium),
          
          // Notes
          TextField(
            controller: notesController,
            decoration: const InputDecoration(
              labelText: '📝 ملاحظات إضافية',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
          
          const Spacer(),
          
          // Book button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                if (nameController.text.isNotEmpty && phoneController.text.isNotEmpty) {
                  Navigator.pop(context);
                  _showSuccessSnackBar(ArabicText.appointmentBooked);
                } else {
                  _showErrorSnackBar('يرجى ملء جميع الحقول المطلوبة');
                }
              },
              child: const Text(ArabicText.bookAppointment),
            ),
          ),
        ],
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppConstants.errorColor,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppConstants.successColor,
      ),
    );
  }
}
