class ArabicText {
  // App Title and Slogan
  static const String appTitle = 'نُطقك مهم';
  static const String appSlogan = 'صوتك مرآة فكرك، وكلماتك جسور تواصلك، إمنح لنطقك رونقه، ولحديثك بهاءه، وإبدأ رحلة التعبير بكل ثقة';

  // Navigation
  static const String home = 'الرئيسية';
  static const String diagnosis = 'التشخيص';
  static const String treatment = 'العلاج';
  static const String specialists = 'الأخصائيين';
  static const String profile = 'الملف الشخصي';

  // Home Screen
  static const String welcome = 'مرحباً بك';
  static const String chooseService = 'اختر الخدمة التي تحتاجها';
  static const String startDiagnosis = '🔍 ابدأ التشخيص';
  static const String startTreatment = '🎯 ابدأ العلاج';
  static const String findSpecialist = '👨‍⚕️ ابحث عن أخصائي';

  // Diagnosis Screen
  static const String diagnosisTitle = 'PHONIKA';
  static const String selectDisorder = '🔍 اختر نوع الاضطراب للتعرف عليه';
  static const String learnMore = 'تعرف أكثر';
  static const String symptoms = 'الأعراض';
  static const String listenExplanation = 'استمع للشرح';

  // Treatment Screen
  static const String treatmentTitle = 'جلسة العلاج';
  static const String selectWord = 'اختر الكلمة للتدريب';
  static const String recordYourVoice = '🎤 سجل صوتك';
  static const String listenCorrect = '🔊 استمع للنطق الصحيح';
  static const String startRecording = '🎙️ ابدأ التسجيل';
  static const String stopRecording = '⏹️ أوقف التسجيل';
  static const String playRecording = '▶️ شغل التسجيل';
  static const String tryAgain = '🔄 حاول مرة أخرى';
  static const String excellent = '🌟 ممتاز!';
  static const String goodJob = '👏 أحسنت!';
  static const String keepTrying = '💪 استمر في المحاولة';

  // Specialists Screen
  static const String specialistsTitle = 'الأخصائيين';
  static const String findNearby = 'ابحث عن أخصائي قريب';
  static const String bookAppointment = 'احجز موعد';
  static const String callNow = 'اتصل الآن';
  static const String sendEmail = 'أرسل إيميل';
  static const String workingHours = 'ساعات العمل';
  static const String location = 'الموقع';
  static const String experience = 'سنوات الخبرة';
  static const String rating = 'التقييم';
  static const String available = 'متاح';
  static const String notAvailable = 'غير متاح';

  // Feedback Messages
  static const String correctPronunciation = 'نطق صحيح! ✅';
  static const String incorrectPronunciation = 'نطق غير صحيح ❌';
  static const String recordingTooShort = 'التسجيل قصير جداً';
  static const String recordingTooLong = 'التسجيل طويل جداً';
  static const String noAudioDetected = 'لم يتم اكتشاف صوت';

  // Buttons
  static const String next = 'التالي';
  static const String previous = 'السابق';
  static const String save = 'حفظ';
  static const String cancel = 'إلغاء';
  static const String ok = 'موافق';
  static const String yes = 'نعم';
  static const String no = 'لا';
  static const String close = 'إغلاق';
  static const String back = 'رجوع';

  // Errors
  static const String errorOccurred = 'حدث خطأ';
  static const String networkError = 'خطأ في الاتصال';
  static const String permissionDenied = 'تم رفض الإذن';
  static const String microphonePermission = 'يرجى السماح بالوصول للميكروفون';
  static const String storagePermission = 'يرجى السماح بالوصول للتخزين';

  // Success Messages
  static const String sessionCompleted = 'تم إكمال الجلسة بنجاح';
  static const String appointmentBooked = 'تم حجز الموعد بنجاح';
  static const String progressSaved = 'تم حفظ التقدم';

  // Character Messages
  static const String characterWelcome = '🌟 مرحباً! أنا هنا لمساعدتك في تحسين نطقك 💪';
  static const String characterEncouragement = '👏 أحسنت! استمر في التدريب 🎯';
  static const String characterMotivation = '💪 لا تستسلم، كل محاولة تقربك من الهدف 🌈';
  static const String characterCelebration = '🎉 رائع! لقد تحسن نطقك كثيراً ⭐';

  // Progress
  static const String yourProgress = 'تقدمك';
  static const String sessionsCompleted = 'الجلسات المكتملة';
  static const String accuracy = 'دقة النطق';
  static const String wordsLearned = 'الكلمات المتعلمة';
  static const String timeSpent = 'الوقت المستغرق';
}
