class EducationalArticle {
  final String id;
  final String title;
  final String content;
  final String summary;
  final String author;
  final String imageUrl;
  final String category;
  final List<String> tags;
  final DateTime publishedDate;
  final int readingTimeMinutes;
  final int views;
  final double rating;
  final bool isFeatured;

  const EducationalArticle({
    required this.id,
    required this.title,
    required this.content,
    required this.summary,
    required this.author,
    required this.imageUrl,
    required this.category,
    required this.tags,
    required this.publishedDate,
    required this.readingTimeMinutes,
    required this.views,
    required this.rating,
    required this.isFeatured,
  });

  factory EducationalArticle.fromJson(Map<String, dynamic> json) {
    return EducationalArticle(
      id: json['id'],
      title: json['title'],
      content: json['content'],
      summary: json['summary'],
      author: json['author'],
      imageUrl: json['imageUrl'],
      category: json['category'],
      tags: List<String>.from(json['tags']),
      publishedDate: DateTime.parse(json['publishedDate']),
      readingTimeMinutes: json['readingTimeMinutes'],
      views: json['views'],
      rating: json['rating'].toDouble(),
      isFeatured: json['isFeatured'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'summary': summary,
      'author': author,
      'imageUrl': imageUrl,
      'category': category,
      'tags': tags,
      'publishedDate': publishedDate.toIso8601String(),
      'readingTimeMinutes': readingTimeMinutes,
      'views': views,
      'rating': rating,
      'isFeatured': isFeatured,
    };
  }

  // Sample data for demonstration
  static List<EducationalArticle> getSampleArticles() {
    return [
      EducationalArticle(
        id: '1',
        title: 'العلامات المبكرة لاضطرابات النطق عند الأطفال',
        content: '''
اضطرابات النطق عند الأطفال من المشاكل الشائعة التي تحتاج إلى تدخل مبكر لضمان أفضل النتائج. في هذا المقال، سنتعرف على العلامات المبكرة التي يجب على الوالدين ملاحظتها.

## العلامات في عمر السنتين:
- عدم قدرة الطفل على نطق كلمات بسيطة مثل "ماما" و "بابا"
- صعوبة في فهم التعليمات البسيطة
- عدم محاولة تقليد الأصوات

## العلامات في عمر الثلاث سنوات:
- صعوبة في تكوين جمل من كلمتين أو ثلاث كلمات
- عدم وضوح الكلام للغرباء
- تكرار الأصوات أو الكلمات بشكل مفرط

## العلامات في عمر الأربع سنوات:
- صعوبة في نطق أصوات معينة
- تجنب الحديث أو الخجل المفرط
- صعوبة في سرد القصص البسيطة

## متى يجب استشارة الأخصائي؟
إذا لاحظت أي من هذه العلامات، من المهم استشارة أخصائي النطق في أقرب وقت ممكن. التدخل المبكر يحقق نتائج أفضل ويساعد الطفل على تطوير مهاراته اللغوية بشكل طبيعي.

## نصائح للوالدين:
1. تحدث مع طفلك بانتظام
2. اقرأ له القصص يومياً
3. شجعه على التعبير عن مشاعره
4. كن صبوراً ومشجعاً
5. تجنب تصحيح أخطائه بشكل مباشر
        ''',
        summary: 'دليل شامل للوالدين حول العلامات المبكرة لاضطرابات النطق وأهمية التدخل المبكر',
        author: 'د. أحمد محمد السعيد',
        imageUrl: 'assets/images/article1.jpg',
        category: 'التشخيص المبكر',
        tags: ['اضطرابات النطق', 'الأطفال', 'التشخيص', 'العلامات المبكرة'],
        publishedDate: DateTime(2024, 1, 10),
        readingTimeMinutes: 5,
        views: 2500,
        rating: 4.8,
        isFeatured: true,
      ),
      EducationalArticle(
        id: '2',
        title: 'كيفية دعم الطفل أثناء جلسات علاج النطق',
        content: '''
دور الأسرة في علاج النطق لا يقل أهمية عن دور الأخصائي. في هذا المقال، سنتعرف على كيفية دعم الطفل وتعزيز فعالية العلاج.

## قبل الجلسة:
- تأكد من أن الطفل مرتاح وغير متعب
- اشرح له ما سيحدث بطريقة بسيطة ومشجعة
- تجنب الضغط أو التوتر

## أثناء الجلسة:
- كن حاضراً ومشاركاً إذا طلب الأخصائي ذلك
- تجنب التدخل أو التصحيح
- شجع الطفل بالابتسام والإيماءات الإيجابية

## بعد الجلسة:
- اسأل الأخصائي عن التمارين المنزلية
- مارس التمارين مع الطفل بانتظام
- احتفل بالإنجازات الصغيرة

## نصائح مهمة:
1. الصبر هو المفتاح
2. اجعل التمارين ممتعة وتفاعلية
3. استخدم الألعاب والأنشطة المحببة للطفل
4. تواصل مع الأخصائي بانتظام
5. لا تقارن طفلك بالآخرين
        ''',
        summary: 'نصائح عملية للوالدين حول كيفية دعم الطفل أثناء رحلة علاج النطق',
        author: 'د. فاطمة علي الزهراني',
        imageUrl: 'assets/images/article2.jpg',
        category: 'الدعم الأسري',
        tags: ['الدعم', 'الأسرة', 'العلاج', 'التشجيع'],
        publishedDate: DateTime(2024, 1, 15),
        readingTimeMinutes: 4,
        views: 1800,
        rating: 4.7,
        isFeatured: false,
      ),
      EducationalArticle(
        id: '3',
        title: 'تمارين النطق المنزلية الفعالة',
        content: '''
التمارين المنزلية جزء مهم من برنامج علاج النطق. إليك مجموعة من التمارين البسيطة والفعالة.

## تمارين التنفس:
- تمرين النفخ في البالونات
- تمرين إطفاء الشموع
- تمرين النفخ في الفقاعات

## تمارين اللسان:
- حركة اللسان يميناً ويساراً
- لمس طرف الأنف باللسان
- تحريك اللسان دائرياً

## تمارين الشفاه:
- ضم الشفاه وفتحها
- تقبيل الهواء
- الابتسام والعبوس

## تمارين الأصوات:
- تكرار الأصوات البسيطة
- الغناء والأناشيد
- تقليد أصوات الحيوانات

## نصائح للممارسة:
1. اجعل التمارين قصيرة (10-15 دقيقة)
2. مارسها يومياً في نفس الوقت
3. استخدم المرآة للتغذية البصرية
4. اجعلها ممتعة وتفاعلية
5. كافئ الطفل على المحاولة وليس فقط النجاح
        ''',
        summary: 'مجموعة شاملة من التمارين المنزلية البسيطة والفعالة لتحسين النطق',
        author: 'د. سارة أحمد الغامدي',
        imageUrl: 'assets/images/article3.jpg',
        category: 'التمارين المنزلية',
        tags: ['تمارين', 'منزلية', 'النطق', 'التدريب'],
        publishedDate: DateTime(2024, 1, 20),
        readingTimeMinutes: 6,
        views: 3200,
        rating: 4.9,
        isFeatured: true,
      ),
    ];
  }

  static List<String> getCategories() {
    return [
      'الكل',
      'التشخيص المبكر',
      'الدعم الأسري',
      'التمارين المنزلية',
      'نصائح الخبراء',
      'قصص نجاح',
    ];
  }
}
