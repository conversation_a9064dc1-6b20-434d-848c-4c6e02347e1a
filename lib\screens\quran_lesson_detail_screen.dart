import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:record/record.dart' as record;
import '../models/quran_lesson.dart';
import '../services/file_service.dart';
import '../utils/constants.dart';
import '../widgets/animated_character.dart';
import '../widgets/pronunciation_feedback.dart';

class QuranLessonDetailScreen extends StatefulWidget {
  final QuranLesson lesson;

  const QuranLessonDetailScreen({super.key, required this.lesson});

  @override
  State<QuranLessonDetailScreen> createState() => _QuranLessonDetailScreenState();
}

class _QuranLessonDetailScreenState extends State<QuranLessonDetailScreen> {
  final AudioPlayer _audioPlayer = AudioPlayer();
  final record.AudioRecorder _audioRecorder = record.AudioRecorder();
  
  int _currentVerseIndex = 0;
  bool _isPlaying = false;
  bool _isRecording = false;
  bool _showTransliteration = true;
  String? _recordedFilePath;
  bool? _lastResult;
  int _attempts = 0;
  int _correctCount = 0;

  @override
  void dispose() {
    _audioPlayer.dispose();
    _audioRecorder.dispose();
    super.dispose();
  }

  QuranVerse get _currentVerse => widget.lesson.verses[_currentVerseIndex];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.lesson.surahNameArabic),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: Icon(_showTransliteration ? Icons.visibility : Icons.visibility_off),
            onPressed: () {
              setState(() {
                _showTransliteration = !_showTransliteration;
              });
            },
          ),
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: _showProgress,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppConstants.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Character and feedback
              _buildCharacterSection(),
              
              // Verse display
              Expanded(
                child: _buildVerseSection(),
              ),
              
              // Audio controls
              _buildAudioControls(),
              
              // Navigation
              _buildNavigationControls(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCharacterSection() {
    String message = '🎵 تعلم تلاوة القرآن الكريم بصوت جميل 📖';
    bool isHappy = true;

    if (_lastResult != null) {
      if (_lastResult!) {
        message = '🌟 ممتاز! تلاوة رائعة، بارك الله فيك 👏';
        isHappy = true;
      } else {
        message = '💪 حاول مرة أخرى، ستتحسن بإذن الله 🤲';
        isHappy = false;
      }
    }

    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: AnimatedCharacter(
        size: 180,
        message: message,
        isHappy: isHappy,
      ),
    );
  }

  Widget _buildVerseSection() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        children: [
          // Verse number and info
          _buildVerseHeader(),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          // Arabic text
          _buildArabicText(),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          // Transliteration (if enabled)
          if (_showTransliteration) _buildTransliteration(),
          
          const SizedBox(height: AppConstants.paddingMedium),
          
          // Translation
          _buildTranslation(),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          // Recording feedback
          if (_lastResult != null) _buildFeedback(),
        ],
      ),
    );
  }

  Widget _buildVerseHeader() {
    return Card(
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                gradient: AppConstants.primaryGradient,
                borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
              ),
              child: Center(
                child: Text(
                  _currentVerse.number.toString(),
                  style: AppConstants.bodyStyle.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '📜 الآية ${_currentVerse.number}',
                    style: AppConstants.subHeadingStyle,
                  ),
                  Text(
                    '🕌 من سورة ${widget.lesson.surahNameArabic}',
                    style: AppConstants.captionStyle,
                  ),
                ],
              ),
            ),
            Text(
              '${_currentVerseIndex + 1} / ${widget.lesson.verses.length}',
              style: AppConstants.captionStyle,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildArabicText() {
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Text(
          _currentVerse.arabicText,
          style: const TextStyle(
            fontFamily: 'Amiri', // Arabic Quran font
            fontSize: 28,
            height: 2.0,
            color: AppConstants.textPrimaryColor,
          ),
          textAlign: TextAlign.center,
          textDirection: TextDirection.rtl,
        ),
      ),
    );
  }

  Widget _buildTransliteration() {
    return Card(
      color: AppConstants.primaryColor.withValues(alpha: 0.1),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          children: [
            Text(
              '🔤 النطق بالأحرف اللاتينية:',
              style: AppConstants.captionStyle.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              _currentVerse.transliteration,
              style: AppConstants.bodyStyle.copyWith(
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTranslation() {
    return Card(
      color: AppConstants.secondaryColor.withValues(alpha: 0.1),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          children: [
            Text(
              '💡 المعنى:',
              style: AppConstants.captionStyle.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              _currentVerse.translation,
              style: AppConstants.bodyStyle,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeedback() {
    return PronunciationFeedback(
      isCorrect: _lastResult!,
      attempts: _attempts,
    );
  }

  Widget _buildAudioControls() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            children: [
              // Listen and record buttons
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _playVerseAudio,
                      icon: Icon(_isPlaying ? Icons.stop : Icons.volume_up),
                      label: Text(_isPlaying ? '⏹️ إيقاف' : '🔊 استمع'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppConstants.secondaryColor,
                      ),
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isRecording ? _stopRecording : _startRecording,
                      icon: Icon(_isRecording ? Icons.stop : Icons.mic),
                      label: Text(_isRecording ? '⏹️ إيقاف التسجيل' : '🎤 سجل صوتك'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _isRecording ? AppConstants.errorColor : AppConstants.primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
              
              // Play recording button
              if (_recordedFilePath != null) ...[
                const SizedBox(height: AppConstants.paddingMedium),
                ElevatedButton.icon(
                  onPressed: _playRecording,
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('▶️ شغل تسجيلك'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppConstants.accentColor,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavigationControls() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Row(
        children: [
          if (_currentVerseIndex > 0)
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _previousVerse,
                icon: const Icon(Icons.arrow_back),
                label: const Text('⬅️ الآية السابقة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.textSecondaryColor,
                ),
              ),
            ),
          
          if (_currentVerseIndex > 0 && _currentVerseIndex < widget.lesson.verses.length - 1)
            const SizedBox(width: AppConstants.paddingMedium),
          
          if (_currentVerseIndex < widget.lesson.verses.length - 1)
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _nextVerse,
                icon: const Icon(Icons.arrow_forward),
                label: const Text('الآية التالية ➡️'),
              ),
            ),
        ],
      ),
    );
  }

  void _playVerseAudio() async {
    try {
      setState(() => _isPlaying = true);
      // In a real app, you would play the actual audio file
      // await _audioPlayer.play(AssetSource(_currentVerse.audioPath));
      
      // Simulate audio playback
      await Future.delayed(const Duration(seconds: 3));
      setState(() => _isPlaying = false);
    } catch (e) {
      setState(() => _isPlaying = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('خطأ في تشغيل الصوت')),
        );
      }
    }
  }

  void _startRecording() async {
    try {
      if (await _audioRecorder.hasPermission()) {
        final recordingPath = await FileService.instance.getQuranRecordingPath();
        await _audioRecorder.start(const record.RecordConfig(), path: recordingPath);
        setState(() {
          _isRecording = true;
          _recordedFilePath = null;
          _lastResult = null;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('خطأ في بدء التسجيل')),
        );
      }
    }
  }

  void _stopRecording() async {
    try {
      final path = await _audioRecorder.stop();
      setState(() {
        _isRecording = false;
        _recordedFilePath = path;
        _attempts++;
      });
      
      if (path != null) {
        _analyzeRecitation();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('خطأ في إيقاف التسجيل')),
        );
      }
    }
  }

  void _analyzeRecitation() {
    // Simulate recitation analysis
    final random = DateTime.now().millisecond % 100;
    final isCorrect = random > 25; // 75% chance of being correct
    
    setState(() {
      _lastResult = isCorrect;
      if (isCorrect) _correctCount++;
    });
  }

  void _playRecording() async {
    if (_recordedFilePath != null) {
      try {
        await _audioPlayer.play(DeviceFileSource(_recordedFilePath!));
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('خطأ في تشغيل التسجيل')),
          );
        }
      }
    }
  }

  void _nextVerse() {
    if (_currentVerseIndex < widget.lesson.verses.length - 1) {
      setState(() {
        _currentVerseIndex++;
        _lastResult = null;
        _recordedFilePath = null;
        _attempts = 0;
      });
    }
  }

  void _previousVerse() {
    if (_currentVerseIndex > 0) {
      setState(() {
        _currentVerseIndex--;
        _lastResult = null;
        _recordedFilePath = null;
        _attempts = 0;
      });
    }
  }

  void _showProgress() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تقدمك في السورة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('الآيات المكتملة: ${_currentVerseIndex + 1} من ${widget.lesson.verses.length}'),
            Text('المحاولات: $_attempts'),
            Text('النطق الصحيح: $_correctCount'),
            Text('نسبة النجاح: ${_attempts > 0 ? (_correctCount / _attempts * 100).toStringAsFixed(1) : 0}%'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
