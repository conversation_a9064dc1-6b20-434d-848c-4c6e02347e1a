import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import '../utils/constants.dart';

class AnimatedCharacter extends StatefulWidget {
  final double size;
  final String message;
  final bool isHappy;
  final VoidCallback? onTap;

  const AnimatedCharacter({
    super.key,
    this.size = 180,
    this.message = '',
    this.isHappy = true,
    this.onTap,
  });

  @override
  State<AnimatedCharacter> createState() => _AnimatedCharacterState();
}

class _AnimatedCharacterState extends State<AnimatedCharacter>
    with TickerProviderStateMixin {
  late AnimationController _bounceController;
  late AnimationController _scaleController;
  late AnimationController _glowController;
  late Animation<double> _bounceAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();

    _bounceController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _glowController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _bounceAnimation = Tween<double>(
      begin: 0,
      end: 15,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.95,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.3,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));

    _startAnimations();
  }

  void _startAnimations() {
    _bounceController.repeat(reverse: true);
    _scaleController.repeat(reverse: true);
    _glowController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _bounceController.dispose();
    _scaleController.dispose();
    _glowController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedBuilder(
              animation: Listenable.merge([_bounceAnimation, _scaleAnimation, _glowAnimation]),
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(0, -_bounceAnimation.value),
                  child: Transform.scale(
                    scale: _scaleAnimation.value,
                    child: Container(
                      width: widget.size,
                      height: widget.size,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: AppConstants.primaryColor.withValues(alpha: _glowAnimation.value * 0.4),
                            blurRadius: 30 * _glowAnimation.value,
                            spreadRadius: 5 * _glowAnimation.value,
                          ),
                          BoxShadow(
                            color: AppConstants.secondaryColor.withValues(alpha: _glowAnimation.value * 0.2),
                            blurRadius: 50 * _glowAnimation.value,
                            spreadRadius: 10 * _glowAnimation.value,
                          ),
                        ],
                      ),
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: RadialGradient(
                            colors: [
                              Colors.white.withValues(alpha: 0.1),
                              Colors.transparent,
                            ],
                          ),
                        ),
                        child: Lottie.asset(
                          'assets/animations/boy_waiting.json',
                          width: widget.size,
                          height: widget.size,
                          fit: BoxFit.contain,
                          repeat: true,
                          animate: true,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
            if (widget.message.isNotEmpty) ...[
              const SizedBox(height: AppConstants.paddingMedium),
              _buildEnhancedSpeechBubble(),
            ],
          ],
        ),
      ),
    );
  }



  Widget _buildEnhancedSpeechBubble() {
    return Container(
      constraints: BoxConstraints(maxWidth: widget.size * 1.8),
      margin: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
      child: Stack(
        children: [
          // Main bubble
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppConstants.paddingLarge),
            margin: const EdgeInsets.only(top: 15),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.white,
                  AppConstants.primaryColor.withValues(alpha: 0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
              boxShadow: [
                BoxShadow(
                  color: AppConstants.primaryColor.withValues(alpha: 0.15),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                  spreadRadius: 2,
                ),
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
              border: Border.all(
                color: AppConstants.primaryColor.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
            child: Text(
              widget.message,
              style: AppConstants.bodyStyle.copyWith(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                height: 1.4,
                color: AppConstants.textPrimaryColor,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          // Speech bubble tail
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Center(
              child: CustomPaint(
                size: const Size(30, 15),
                painter: EnhancedSpeechBubbleTailPainter(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class EnhancedSpeechBubbleTailPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final shadowPaint = Paint()
      ..color = AppConstants.primaryColor.withValues(alpha: 0.1)
      ..style = PaintingStyle.fill;

    // Draw shadow
    final shadowPath = Path()
      ..moveTo(size.width / 2, 2)
      ..lineTo(2, size.height + 2)
      ..lineTo(size.width - 2, size.height + 2)
      ..close();
    canvas.drawPath(shadowPath, shadowPaint);

    // Draw main tail
    final path = Path()
      ..moveTo(size.width / 2, 0)
      ..lineTo(0, size.height)
      ..lineTo(size.width, size.height)
      ..close();

    canvas.drawPath(path, paint);

    // Draw border
    final borderPaint = Paint()
      ..color = AppConstants.primaryColor.withValues(alpha: 0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    canvas.drawPath(path, borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
