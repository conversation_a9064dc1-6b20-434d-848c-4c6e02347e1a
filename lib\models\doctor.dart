class Doctor {
  final String id;
  final String name;
  final String specialization;
  final String clinic;
  final String address;
  final double latitude;
  final double longitude;
  final String phone;
  final String email;
  final double rating;
  final int reviewsCount;
  final List<String> workingDays;
  final String workingHours;
  final double consultationFee;
  final String imageUrl;
  final String description;
  final List<String> languages;
  final bool isAvailable;
  final double distanceKm;

  const Doctor({
    required this.id,
    required this.name,
    required this.specialization,
    required this.clinic,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.phone,
    required this.email,
    required this.rating,
    required this.reviewsCount,
    required this.workingDays,
    required this.workingHours,
    required this.consultationFee,
    required this.imageUrl,
    required this.description,
    required this.languages,
    required this.isAvailable,
    required this.distanceKm,
  });

  factory Doctor.fromJson(Map<String, dynamic> json) {
    return Doctor(
      id: json['id'],
      name: json['name'],
      specialization: json['specialization'],
      clinic: json['clinic'],
      address: json['address'],
      latitude: json['latitude'].toDouble(),
      longitude: json['longitude'].toDouble(),
      phone: json['phone'],
      email: json['email'],
      rating: json['rating'].toDouble(),
      reviewsCount: json['reviewsCount'],
      workingDays: List<String>.from(json['workingDays']),
      workingHours: json['workingHours'],
      consultationFee: json['consultationFee'].toDouble(),
      imageUrl: json['imageUrl'],
      description: json['description'],
      languages: List<String>.from(json['languages']),
      isAvailable: json['isAvailable'],
      distanceKm: json['distanceKm'].toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'specialization': specialization,
      'clinic': clinic,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'phone': phone,
      'email': email,
      'rating': rating,
      'reviewsCount': reviewsCount,
      'workingDays': workingDays,
      'workingHours': workingHours,
      'consultationFee': consultationFee,
      'imageUrl': imageUrl,
      'description': description,
      'languages': languages,
      'isAvailable': isAvailable,
      'distanceKm': distanceKm,
    };
  }

  // Sample data for demonstration
  static List<Doctor> getSampleDoctors() {
    return [
      Doctor(
        id: '1',
        name: 'د. أحمد بن علي بوعلام',
        specialization: 'أخصائي علاج النطق واللغة',
        clinic: 'مركز الجزائر للنطق والسمع',
        address: 'شارع ديدوش مراد، الجزائر العاصمة',
        latitude: 36.7538,
        longitude: 3.0588,
        phone: '+213555123456',
        email: '<EMAIL>',
        rating: 4.8,
        reviewsCount: 156,
        workingDays: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس'],
        workingHours: '8:00 ص - 6:00 م',
        consultationFee: 3000.0,
        imageUrl: 'assets/images/doctor1.jpg',
        description: 'خبرة 15 سنة في علاج اضطرابات النطق عند الأطفال والبالغين',
        languages: ['العربية', 'الفرنسية'],
        isAvailable: true,
        distanceKm: 2.5,
      ),
      Doctor(
        id: '2',
        name: 'د. فاطمة بن يوسف',
        specialization: 'أخصائية النطق للأطفال',
        clinic: 'عيادة النطق المتخصصة',
        address: 'حي بن عكنون، الجزائر العاصمة',
        latitude: 36.7167,
        longitude: 3.0333,
        phone: '+213556234567',
        email: '<EMAIL>',
        rating: 4.9,
        reviewsCount: 203,
        workingDays: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء'],
        workingHours: '9:00 ص - 5:00 م',
        consultationFee: 3500.0,
        imageUrl: 'assets/images/doctor2.jpg',
        description: 'متخصصة في علاج اضطرابات النطق عند الأطفال من عمر سنتين إلى 12 سنة',
        languages: ['العربية', 'الفرنسية'],
        isAvailable: true,
        distanceKm: 3.8,
      ),
      Doctor(
        id: '3',
        name: 'د. محمد عبدالرحمن بلعيد',
        specialization: 'استشاري علاج النطق',
        clinic: 'مستشفى مصطفى باشا الجامعي',
        address: 'شارع محمد بوضياف، الجزائر العاصمة',
        latitude: 36.7372,
        longitude: 3.0869,
        phone: '+213557345678',
        email: '<EMAIL>',
        rating: 4.7,
        reviewsCount: 89,
        workingDays: ['الأحد', 'الثلاثاء', 'الخميس'],
        workingHours: '10:00 ص - 4:00 م',
        consultationFee: 5000.0,
        imageUrl: 'assets/images/doctor3.jpg',
        description: 'استشاري معتمد مع خبرة 20 سنة في علاج الحالات المعقدة',
        languages: ['العربية', 'الفرنسية'],
        isAvailable: false,
        distanceKm: 5.2,
      ),
      Doctor(
        id: '4',
        name: 'د. سارة بن عمر حداد',
        specialization: 'أخصائية النطق والبلع',
        clinic: 'مركز التأهيل الطبي',
        address: 'حي الحراش، الجزائر العاصمة',
        latitude: 36.7081,
        longitude: 3.1417,
        phone: '+213558456789',
        email: '<EMAIL>',
        rating: 4.6,
        reviewsCount: 127,
        workingDays: ['السبت', 'الاثنين', 'الأربعاء', 'الخميس'],
        workingHours: '8:30 ص - 3:30 م',
        consultationFee: 2800.0,
        imageUrl: 'assets/images/doctor4.jpg',
        description: 'متخصصة في علاج اضطرابات النطق والبلع للأطفال والبالغين',
        languages: ['العربية', 'الفرنسية'],
        isAvailable: true,
        distanceKm: 4.1,
      ),
      Doctor(
        id: '5',
        name: 'د. خالد بن مصطفى زيتوني',
        specialization: 'أخصائي علاج النطق',
        clinic: 'عيادات الدكتور خالد',
        address: 'شارع العربي بن مهيدي، الجزائر العاصمة',
        latitude: 36.7628,
        longitude: 3.0583,
        phone: '+213559567890',
        email: '<EMAIL>',
        rating: 4.5,
        reviewsCount: 94,
        workingDays: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'السبت'],
        workingHours: '7:00 ص - 9:00 م',
        consultationFee: 2500.0,
        imageUrl: 'assets/images/doctor5.jpg',
        description: 'خبرة 10 سنوات في علاج جميع أنواع اضطرابات النطق',
        languages: ['العربية', 'الفرنسية'],
        isAvailable: true,
        distanceKm: 1.8,
      ),
    ];
  }

  static List<String> getSpecializations() {
    return [
      'الكل',
      'أخصائي علاج النطق واللغة',
      'أخصائية النطق للأطفال',
      'استشاري علاج النطق',
      'أخصائية النطق والبلع',
    ];
  }
}
