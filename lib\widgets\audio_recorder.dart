import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../utils/constants.dart';
import '../utils/arabic_text.dart';

class AudioRecorder extends StatefulWidget {
  final bool isRecording;
  final VoidCallback onStartRecording;
  final VoidCallback onStopRecording;

  const AudioRecorder({
    super.key,
    required this.isRecording,
    required this.onStartRecording,
    required this.onStopRecording,
  });

  @override
  State<AudioRecorder> createState() => _AudioRecorderState();
}

class _AudioRecorderState extends State<AudioRecorder>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _waveController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _waveAnimation;

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    
    _waveController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _waveAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _waveController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didUpdateWidget(AudioRecorder oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isRecording && !oldWidget.isRecording) {
      _startAnimations();
    } else if (!widget.isRecording && oldWidget.isRecording) {
      _stopAnimations();
    }
  }

  void _startAnimations() {
    _pulseController.repeat(reverse: true);
    _waveController.repeat(reverse: true);
  }

  void _stopAnimations() {
    _pulseController.stop();
    _waveController.stop();
    _pulseController.reset();
    _waveController.reset();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _waveController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Recording button with animations
        _buildRecordingButton(),
        
        const SizedBox(height: AppConstants.paddingMedium),
        
        // Status text
        _buildStatusText(),
        
        const SizedBox(height: AppConstants.paddingMedium),
        
        // Audio waves visualization
        if (widget.isRecording) _buildAudioWaves(),
      ],
    );
  }

  Widget _buildRecordingButton() {
    return GestureDetector(
      onTap: widget.isRecording ? widget.onStopRecording : widget.onStartRecording,
      child: AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: widget.isRecording ? _pulseAnimation.value : 1.0,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: widget.isRecording
                    ? const LinearGradient(
                        colors: [Color(0xFFFF5722), Color(0xFFFF8A65)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      )
                    : AppConstants.primaryGradient,
                boxShadow: [
                  BoxShadow(
                    color: (widget.isRecording 
                        ? Colors.red 
                        : AppConstants.primaryColor).withOpacity(0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Icon(
                widget.isRecording ? Icons.stop : Icons.mic,
                size: 48,
                color: Colors.white,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatusText() {
    return Text(
      widget.isRecording ? ArabicText.stopRecording : ArabicText.startRecording,
      style: AppConstants.subHeadingStyle.copyWith(
        color: widget.isRecording ? Colors.red : AppConstants.primaryColor,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildAudioWaves() {
    return AnimatedBuilder(
      animation: _waveAnimation,
      builder: (context, child) {
        return Container(
          height: 60,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(5, (index) {
              final delay = index * 0.2;
              final animationValue = (_waveAnimation.value + delay) % 1.0;
              final height = 20 + (math.sin(animationValue * 2 * math.pi) * 20);
              
              return Container(
                width: 4,
                height: height,
                margin: const EdgeInsets.symmetric(horizontal: 2),
                decoration: BoxDecoration(
                  color: AppConstants.primaryColor,
                  borderRadius: BorderRadius.circular(2),
                ),
              );
            }),
          ),
        );
      },
    );
  }
}
