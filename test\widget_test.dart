import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:not9k_mohim/main.dart';

void main() {
  testWidgets('App should compile and start', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const Not9kMohimApp());
    await tester.pump();

    // Verify that the app starts without crashing
    expect(find.byType(MaterialApp), findsOneWidget);
  });
}
