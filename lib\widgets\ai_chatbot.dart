import 'package:flutter/material.dart';
import 'package:flutter_chat_ui/flutter_chat_ui.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:uuid/uuid.dart';

class AI<PERSON>hatBot extends StatefulWidget {
  final VoidCallback? onFirstUserMessage;
  const AIChatBot({Key? key, this.onFirstUserMessage}) : super(key: key);

  @override
  State<AIChatBot> createState() => _AIChatBotState();
}

class _AIChatBotState extends State<AIChatBot> {
  final List<types.Message> _messages = [];
  final types.User _user = const types.User(id: 'user');
  final types.User _bot = const types.User(id: 'bot', firstName: 'فصيح');
  final _uuid = const Uuid();
  bool _isLoading = false;
  bool _hasSentFirstMessage = false;

  Future<void> _handleSendPressed(types.PartialText message) async {
    if (!_hasSentFirstMessage) {
      _hasSentFirstMessage = true;
      widget.onFirstUserMessage?.call();
    }
    setState(() {
      _messages.insert(
        0,
        types.TextMessage(
          author: _user,
          createdAt: DateTime.now().millisecondsSinceEpoch,
          id: _uuid.v4(),
          text: message.text,
        ),
      );
      _isLoading = true;
    });
    final response = await _sendMessageToAI(message.text);
    setState(() {
      _messages.insert(
        0,
        types.TextMessage(
          author: _bot,
          createdAt: DateTime.now().millisecondsSinceEpoch,
          id: _uuid.v4(),
          text: response,
        ),
      );
      _isLoading = false;
    });
  }

  Future<String> _sendMessageToAI(String userMessage) async {
    const apiKey =
        '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';
    const apiUrl = 'https://openrouter.ai/api/v1/chat/completions';
    final headers = {
      'Authorization': 'Bearer $apiKey',
      'Content-Type': 'application/json',
    };
    final body = jsonEncode({
      'model': 'deepseek/deepseek-chat-v3-0324:free',
      'messages': [
        {'role': 'user', 'content': userMessage},
      ],
      'max_tokens': 512,
    });
    try {
      final response = await http.post(
        Uri.parse(apiUrl),
        headers: headers,
        body: body,
      );
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final content = data['choices'][0]['message']['content'];
        return content ?? 'لم أتمكن من الفهم. حاول مرة أخرى.';
      } else {
        print(
          'AI ERROR: \nStatus: ${response.statusCode}\nBody: ${response.body}',
        );
        return 'حدث خطأ في الاتصال بالذكاء الاصطناعي: \n${response.body}';
      }
    } catch (e) {
      print('AI EXCEPTION: \n$e');
      return 'تعذر الاتصال بالخادم.';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Chat(
          messages: _messages,
          onSendPressed: _handleSendPressed,
          user: _user,
          showUserAvatars: false,
          showUserNames: false,
          theme: DefaultChatTheme(
            inputBackgroundColor: Colors.white,
            inputTextColor: Colors.black,
            primaryColor: const Color(0xFF7C3AED),
            sentMessageBodyTextStyle: const TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
            receivedMessageBodyTextStyle: const TextStyle(
              color: Color(0xFF4B5563),
              fontSize: 16,
            ),
            inputBorderRadius: BorderRadius.circular(30),
            inputTextCursorColor: const Color(0xFF7C3AED),
            inputTextDecoration: InputDecoration(hintText: 'اكتب رسالتك...'),
            inputPadding: const EdgeInsets.symmetric(
              horizontal: 20,
              vertical: 12,
            ),
            inputContainerDecoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: Colors.deepPurple.withOpacity(0.08),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            sendButtonIcon: Container(
              decoration: BoxDecoration(
                color: const Color(0xFF7C3AED),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.deepPurple.withOpacity(0.18),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Padding(
                padding: EdgeInsets.all(8.0),
                child: Icon(Icons.send, color: Colors.white, size: 24),
              ),
            ),
            inputTextStyle: const TextStyle(fontSize: 16),
            backgroundColor: Colors.transparent,
            messageInsetsHorizontal: 12,
            messageInsetsVertical: 8,
          ),
        ),
        if (_isLoading)
          Positioned(
            left: 0,
            right: 0,
            bottom: 70,
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(
                  vertical: 6,
                  horizontal: 18,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.deepPurple.withOpacity(0.08),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      width: 18,
                      height: 18,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Color(0xFF7C3AED),
                      ),
                    ),
                    SizedBox(width: 10),
                    Text(
                      'يتم توليد الرد...',
                      style: TextStyle(color: Color(0xFF7C3AED), fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }
}
