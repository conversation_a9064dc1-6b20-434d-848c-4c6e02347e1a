import 'package:flutter/material.dart';

class User {
  final String id;
  final String name;
  final String email;
  final String? phoneNumber;
  final DateTime dateOfBirth;
  final Gender gender;
  final String? profileImagePath;
  final DateTime createdAt;
  final UserProgress progress;
  final UserPreferences preferences;

  User({
    required this.id,
    required this.name,
    required this.email,
    this.phoneNumber,
    required this.dateOfBirth,
    required this.gender,
    this.profileImagePath,
    required this.createdAt,
    required this.progress,
    required this.preferences,
  });

  int get age {
    final now = DateTime.now();
    int age = now.year - dateOfBirth.year;
    if (now.month < dateOfBirth.month || 
        (now.month == dateOfBirth.month && now.day < dateOfBirth.day)) {
      age--;
    }
    return age;
  }

  User copyWith({
    String? name,
    String? email,
    String? phoneNumber,
    DateTime? dateOfBirth,
    Gender? gender,
    String? profileImagePath,
    UserProgress? progress,
    UserPreferences? preferences,
  }) {
    return User(
      id: id,
      name: name ?? this.name,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      profileImagePath: profileImagePath ?? this.profileImagePath,
      createdAt: createdAt,
      progress: progress ?? this.progress,
      preferences: preferences ?? this.preferences,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phoneNumber': phoneNumber,
      'dateOfBirth': dateOfBirth.toIso8601String(),
      'gender': gender.name,
      'profileImagePath': profileImagePath,
      'createdAt': createdAt.toIso8601String(),
      'progress': progress.toJson(),
      'preferences': preferences.toJson(),
    };
  }

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      phoneNumber: json['phoneNumber'],
      dateOfBirth: DateTime.parse(json['dateOfBirth']),
      gender: Gender.values.firstWhere((e) => e.name == json['gender']),
      profileImagePath: json['profileImagePath'],
      createdAt: DateTime.parse(json['createdAt']),
      progress: UserProgress.fromJson(json['progress']),
      preferences: UserPreferences.fromJson(json['preferences']),
    );
  }
}

class UserProgress {
  final int totalSessions;
  final int completedExercises;
  final int streakDays;
  final DateTime lastSessionDate;
  final Map<String, int> skillLevels;
  final List<String> completedLessons;
  final Map<String, double> pronunciationScores;

  UserProgress({
    required this.totalSessions,
    required this.completedExercises,
    required this.streakDays,
    required this.lastSessionDate,
    required this.skillLevels,
    required this.completedLessons,
    required this.pronunciationScores,
  });

  UserProgress copyWith({
    int? totalSessions,
    int? completedExercises,
    int? streakDays,
    DateTime? lastSessionDate,
    Map<String, int>? skillLevels,
    List<String>? completedLessons,
    Map<String, double>? pronunciationScores,
  }) {
    return UserProgress(
      totalSessions: totalSessions ?? this.totalSessions,
      completedExercises: completedExercises ?? this.completedExercises,
      streakDays: streakDays ?? this.streakDays,
      lastSessionDate: lastSessionDate ?? this.lastSessionDate,
      skillLevels: skillLevels ?? this.skillLevels,
      completedLessons: completedLessons ?? this.completedLessons,
      pronunciationScores: pronunciationScores ?? this.pronunciationScores,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalSessions': totalSessions,
      'completedExercises': completedExercises,
      'streakDays': streakDays,
      'lastSessionDate': lastSessionDate.toIso8601String(),
      'skillLevels': skillLevels,
      'completedLessons': completedLessons,
      'pronunciationScores': pronunciationScores,
    };
  }

  factory UserProgress.fromJson(Map<String, dynamic> json) {
    return UserProgress(
      totalSessions: json['totalSessions'] ?? 0,
      completedExercises: json['completedExercises'] ?? 0,
      streakDays: json['streakDays'] ?? 0,
      lastSessionDate: DateTime.parse(json['lastSessionDate'] ?? DateTime.now().toIso8601String()),
      skillLevels: Map<String, int>.from(json['skillLevels'] ?? {}),
      completedLessons: List<String>.from(json['completedLessons'] ?? []),
      pronunciationScores: Map<String, double>.from(json['pronunciationScores'] ?? {}),
    );
  }

  static UserProgress empty() {
    return UserProgress(
      totalSessions: 0,
      completedExercises: 0,
      streakDays: 0,
      lastSessionDate: DateTime.now(),
      skillLevels: {},
      completedLessons: [],
      pronunciationScores: {},
    );
  }
}

class UserPreferences {
  final bool notificationsEnabled;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final String language;
  final ThemeMode themeMode;
  final double fontSize;
  final bool showTransliteration;
  final bool autoPlayAudio;

  UserPreferences({
    required this.notificationsEnabled,
    required this.soundEnabled,
    required this.vibrationEnabled,
    required this.language,
    required this.themeMode,
    required this.fontSize,
    required this.showTransliteration,
    required this.autoPlayAudio,
  });

  UserPreferences copyWith({
    bool? notificationsEnabled,
    bool? soundEnabled,
    bool? vibrationEnabled,
    String? language,
    ThemeMode? themeMode,
    double? fontSize,
    bool? showTransliteration,
    bool? autoPlayAudio,
  }) {
    return UserPreferences(
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      language: language ?? this.language,
      themeMode: themeMode ?? this.themeMode,
      fontSize: fontSize ?? this.fontSize,
      showTransliteration: showTransliteration ?? this.showTransliteration,
      autoPlayAudio: autoPlayAudio ?? this.autoPlayAudio,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'notificationsEnabled': notificationsEnabled,
      'soundEnabled': soundEnabled,
      'vibrationEnabled': vibrationEnabled,
      'language': language,
      'themeMode': themeMode.name,
      'fontSize': fontSize,
      'showTransliteration': showTransliteration,
      'autoPlayAudio': autoPlayAudio,
    };
  }

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      notificationsEnabled: json['notificationsEnabled'] ?? true,
      soundEnabled: json['soundEnabled'] ?? true,
      vibrationEnabled: json['vibrationEnabled'] ?? true,
      language: json['language'] ?? 'ar',
      themeMode: ThemeMode.values.firstWhere(
        (e) => e.name == json['themeMode'],
        orElse: () => ThemeMode.system,
      ),
      fontSize: json['fontSize']?.toDouble() ?? 16.0,
      showTransliteration: json['showTransliteration'] ?? true,
      autoPlayAudio: json['autoPlayAudio'] ?? false,
    );
  }

  static UserPreferences defaultPreferences() {
    return UserPreferences(
      notificationsEnabled: true,
      soundEnabled: true,
      vibrationEnabled: true,
      language: 'ar',
      themeMode: ThemeMode.system,
      fontSize: 16.0,
      showTransliteration: true,
      autoPlayAudio: false,
    );
  }
}

enum Gender {
  male,
  female,
}

extension GenderExtension on Gender {
  String get arabicName {
    switch (this) {
      case Gender.male:
        return 'ذكر';
      case Gender.female:
        return 'أنثى';
    }
  }

  IconData get icon {
    switch (this) {
      case Gender.male:
        return Icons.male;
      case Gender.female:
        return Icons.female;
    }
  }
}
