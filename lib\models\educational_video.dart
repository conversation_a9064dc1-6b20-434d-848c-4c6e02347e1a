class EducationalVideo {
  final String id;
  final String title;
  final String description;
  final String youtubeId;
  final String thumbnailUrl;
  final Duration duration;
  final String category;
  final int views;
  final double rating;
  final List<String> tags;
  final DateTime publishedDate;

  const EducationalVideo({
    required this.id,
    required this.title,
    required this.description,
    required this.youtubeId,
    required this.thumbnailUrl,
    required this.duration,
    required this.category,
    required this.views,
    required this.rating,
    required this.tags,
    required this.publishedDate,
  });

  factory EducationalVideo.fromJson(Map<String, dynamic> json) {
    return EducationalVideo(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      youtubeId: json['youtubeId'],
      thumbnailUrl: json['thumbnailUrl'],
      duration: Duration(seconds: json['duration']),
      category: json['category'],
      views: json['views'],
      rating: json['rating'].toDouble(),
      tags: List<String>.from(json['tags']),
      publishedDate: DateTime.parse(json['publishedDate']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'youtubeId': youtubeId,
      'thumbnailUrl': thumbnailUrl,
      'duration': duration.inSeconds,
      'category': category,
      'views': views,
      'rating': rating,
      'tags': tags,
      'publishedDate': publishedDate.toIso8601String(),
    };
  }

  // Sample data for demonstration
  static List<EducationalVideo> getSampleVideos() {
    return [
      EducationalVideo(
        id: '1',
        title: 'فهم اضطرابات النطق عند الأطفال',
        description: 'شرح شامل لأنواع اضطرابات النطق المختلفة وكيفية التعرف عليها في المراحل المبكرة',
        youtubeId: 'xZAoj6w9eSw', // Updated YouTube ID
        thumbnailUrl: 'https://img.youtube.com/vi/xZAoj6w9eSw/maxresdefault.jpg',
        duration: const Duration(minutes: 15, seconds: 30),
        category: 'التشخيص',
        views: 12500,
        rating: 4.8,
        tags: ['اضطرابات النطق', 'الأطفال', 'التشخيص المبكر'],
        publishedDate: DateTime(2024, 1, 15),
      ),
      EducationalVideo(
        id: '2',
        title: 'تمارين النطق المنزلية للأطفال',
        description: 'مجموعة من التمارين البسيطة التي يمكن للوالدين ممارستها مع أطفالهم في المنزل',
        youtubeId: 'parsw4W1u0E', // Updated YouTube ID
        thumbnailUrl: 'https://img.youtube.com/vi/parsw4W1u0E/maxresdefault.jpg',
        duration: const Duration(minutes: 20, seconds: 45),
        category: 'التمارين',
        views: 8900,
        rating: 4.6,
        tags: ['تمارين منزلية', 'النطق', 'الوالدين'],
        publishedDate: DateTime(2024, 1, 20),
      ),
      EducationalVideo(
        id: '3',
        title: 'دور الأسرة في علاج النطق',
        description: 'كيف يمكن للأسرة أن تلعب دوراً مهماً في دعم الطفل أثناء رحلة علاج النطق',
        youtubeId: 'Fw0wtH8U89Q', // Updated YouTube ID
        thumbnailUrl: 'https://img.youtube.com/vi/Fw0wtH8U89Q/maxresdefault.jpg',
        duration: const Duration(minutes: 12, seconds: 15),
        category: 'الدعم الأسري',
        views: 15600,
        rating: 4.9,
        tags: ['الأسرة', 'الدعم', 'العلاج'],
        publishedDate: DateTime(2024, 1, 25),
      ),
      EducationalVideo(
        id: '4',
        title: 'متى نحتاج لاستشارة أخصائي النطق؟',
        description: 'العلامات التي تدل على ضرورة استشارة أخصائي النطق والتدخل المبكر',
        youtubeId: 'bi44lDt5r5s', // Updated YouTube ID
        thumbnailUrl: 'https://img.youtube.com/vi/bi44lDt5r5s/maxresdefault.jpg',
        duration: const Duration(minutes: 18, seconds: 20),
        category: 'الاستشارة',
        views: 9800,
        rating: 4.7,
        tags: ['استشارة', 'أخصائي', 'التدخل المبكر'],
        publishedDate: DateTime(2024, 2, 1),
      ),
      EducationalVideo(
        id: '5',
        title: 'ألعاب تطوير النطق للأطفال',
        description: 'ألعاب ممتعة وتفاعلية تساعد في تطوير مهارات النطق واللغة عند الأطفال',
        youtubeId: '49oHCx_kDJs', // Updated YouTube ID
        thumbnailUrl: 'https://img.youtube.com/vi/49oHCx_kDJs/maxresdefault.jpg',
        duration: const Duration(minutes: 25, seconds: 10),
        category: 'الألعاب التعليمية',
        views: 11200,
        rating: 4.8,
        tags: ['ألعاب', 'تطوير', 'تفاعلي'],
        publishedDate: DateTime(2024, 2, 5),
      ),
    ];
  }

  static List<String> getCategories() {
    return [
      'الكل',
      'التشخيص',
      'التمارين',
      'الدعم الأسري',
      'الاستشارة',
      'الألعاب التعليمية',
    ];
  }
}
