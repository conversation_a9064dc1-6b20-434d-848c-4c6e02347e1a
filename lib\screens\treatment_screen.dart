import 'package:flutter/material.dart';
import 'package:record/record.dart' as record;
import 'package:audioplayers/audioplayers.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/speech_disorder.dart';
import '../models/treatment_word.dart';
import '../services/file_service.dart';
import '../utils/constants.dart';
import '../utils/arabic_text.dart';
import '../widgets/animated_character.dart';
import '../widgets/audio_recorder.dart' as widgets;
import '../widgets/pronunciation_feedback.dart';

class TreatmentScreen extends StatefulWidget {
  final SpeechDisorder? selectedDisorder;

  const TreatmentScreen({super.key, this.selectedDisorder});

  @override
  State<TreatmentScreen> createState() => _TreatmentScreenState();
}

class _TreatmentScreenState extends State<TreatmentScreen> {
  final record.AudioRecorder _audioRecorder = record.AudioRecorder();
  final AudioPlayer _audioPlayer = AudioPlayer();
  
  List<SpeechDisorder> _disorders = [];
  SpeechDisorder? _currentDisorder;
  TreatmentWord? _currentTreatmentWord;
  List<TreatmentWord> _treatmentWords = [];
  int _currentWordIndex = 0;
  bool _isRecording = false;
  bool _isPlaying = false;
  String? _recordedFilePath;
  bool? _lastResult;
  int _attempts = 0;
  int _correctCount = 0;

  @override
  void initState() {
    super.initState();
    _disorders = SpeechDisorder.getAllDisorders();
    _currentDisorder = widget.selectedDisorder ?? _disorders.first;
    _treatmentWords = TreatmentWord.getWordsForDisorder(_currentDisorder!.id);
    _currentTreatmentWord = _treatmentWords.first;
    _requestPermissions();
  }

  @override
  void dispose() {
    _audioRecorder.dispose();
    _audioPlayer.dispose();
    super.dispose();
  }

  Future<void> _requestPermissions() async {
    await Permission.microphone.request();
    await Permission.storage.request();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
      appBar: AppBar(
        title: Text(ArabicText.treatmentTitle),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: _showProgress,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppConstants.backgroundGradient,
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              children: [
                // Character and feedback
                _buildCharacterSection(),

                // Disorder selection
                _buildDisorderSelection(),

                // Current word display
                _buildCurrentWordSection(),

                // Audio controls
                _buildAudioControls(),

                // Recording section
                _buildRecordingSection(),

                // Navigation buttons
                _buildNavigationButtons(),

                // Add some bottom padding
                const SizedBox(height: AppConstants.paddingLarge),
              ],
            ),
          ),
        ),
      ),
    ),
    );
  }

  Widget _buildCharacterSection() {
    String message = ArabicText.characterWelcome;
    bool isHappy = true;

    if (_lastResult != null) {
      if (_lastResult!) {
        message = ArabicText.characterCelebration;
        isHappy = true;
      } else {
        message = ArabicText.characterMotivation;
        isHappy = false;
      }
    }

    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: AnimatedCharacter(
        size: 200,
        message: message,
        isHappy: isHappy,
      ),
    );
  }

  Widget _buildDisorderSelection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'نوع الاضطراب',
                style: AppConstants.subHeadingStyle,
              ),
              const SizedBox(height: AppConstants.paddingSmall),
              DropdownButtonFormField<SpeechDisorder>(
                value: _currentDisorder,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: AppConstants.paddingMedium,
                    vertical: AppConstants.paddingSmall,
                  ),
                ),
                items: _disorders.map((disorder) {
                  return DropdownMenuItem(
                    value: disorder,
                    child: Text(disorder.nameArabic),
                  );
                }).toList(),
                onChanged: (disorder) {
                  setState(() {
                    _currentDisorder = disorder;
                    _currentWordIndex = 0;
                    _treatmentWords = TreatmentWord.getWordsForDisorder(disorder!.id);
                    _currentTreatmentWord = _treatmentWords.first;
                    _lastResult = null;
                    _attempts = 0;
                  });
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCurrentWordSection() {
    if (_currentTreatmentWord == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Card(
        elevation: 8,
        shadowColor: _currentTreatmentWord!.color.withValues(alpha: 0.3),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
            gradient: LinearGradient(
              colors: [
                Colors.white,
                _currentTreatmentWord!.color.withValues(alpha: 0.1),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Column(
            children: [
              Text(
                '📝 الكلمة الحالية',
                style: AppConstants.subHeadingStyle.copyWith(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: AppConstants.paddingLarge),

              // Word display with icon and emoji
              Container(
                padding: const EdgeInsets.all(AppConstants.paddingLarge * 1.5),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      _currentTreatmentWord!.color,
                      _currentTreatmentWord!.color.withValues(alpha: 0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
                  boxShadow: [
                    BoxShadow(
                      color: _currentTreatmentWord!.color.withValues(alpha: 0.4),
                      blurRadius: 15,
                      offset: const Offset(0, 8),
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Emoji and Icon row
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          _currentTreatmentWord!.emoji,
                          style: const TextStyle(fontSize: 40),
                        ),
                        const SizedBox(width: AppConstants.paddingMedium),
                        Icon(
                          _currentTreatmentWord!.icon,
                          color: Colors.white,
                          size: 40,
                        ),
                      ],
                    ),
                    const SizedBox(height: AppConstants.paddingMedium),

                    // Word text
                    Text(
                      _currentTreatmentWord!.word,
                      style: AppConstants.headingStyle.copyWith(
                        color: Colors.white,
                        fontSize: 36,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppConstants.paddingSmall),

                    // Description
                    Text(
                      _currentTreatmentWord!.description,
                      style: AppConstants.bodyStyle.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppConstants.paddingLarge),
              Text(
                '📊 كلمة ${_currentWordIndex + 1} من ${_treatmentWords.length}',
                style: AppConstants.captionStyle.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAudioControls() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _playCorrectPronunciation,
                  icon: Icon(_isPlaying ? Icons.stop : Icons.volume_up),
                  label: Text(_isPlaying ? 'إيقاف' : ArabicText.listenCorrect),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppConstants.secondaryColor,
                  ),
                ),
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              if (_recordedFilePath != null)
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _playRecording,
                    icon: const Icon(Icons.play_arrow),
                    label: const Text(ArabicText.playRecording),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.accentColor,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecordingSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Card(
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Column(
            children: [
              widgets.AudioRecorder(
                isRecording: _isRecording,
                onStartRecording: _startRecording,
                onStopRecording: _stopRecording,
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              if (_lastResult != null)
                PronunciationFeedback(
                  isCorrect: _lastResult!,
                  attempts: _attempts,
                ),

              const SizedBox(height: AppConstants.paddingMedium),

              Text(
                '📊 المحاولات: $_attempts | ✅ الصحيح: $_correctCount',
                style: AppConstants.captionStyle.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Row(
        children: [
          if (_currentWordIndex > 0)
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _previousWord,
                icon: const Icon(Icons.arrow_back),
                label: const Text(ArabicText.previous),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.textSecondaryColor,
                ),
              ),
            ),
          
          if (_currentWordIndex > 0 && _currentWordIndex < _treatmentWords.length - 1)
            const SizedBox(width: AppConstants.paddingMedium),

          if (_currentWordIndex < _treatmentWords.length - 1)
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _nextWord,
                icon: const Icon(Icons.arrow_forward),
                label: const Text(ArabicText.next),
              ),
            ),
        ],
      ),
    );
  }

  void _startRecording() async {
    try {
      if (await _audioRecorder.hasPermission()) {
        final recordingPath = await FileService.instance.getTreatmentRecordingPath();
        await _audioRecorder.start(const record.RecordConfig(), path: recordingPath);
        setState(() {
          _isRecording = true;
          _recordedFilePath = null;
          _lastResult = null;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('خطأ في بدء التسجيل')),
        );
      }
    }
  }

  void _stopRecording() async {
    try {
      final path = await _audioRecorder.stop();
      setState(() {
        _isRecording = false;
        _recordedFilePath = path;
        _attempts++;
      });
      
      if (path != null) {
        _analyzeRecording();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('خطأ في إيقاف التسجيل')),
        );
      }
    }
  }

  void _analyzeRecording() {
    // Simulate speech analysis
    // In a real app, you would use speech recognition APIs
    final random = DateTime.now().millisecond % 100;
    final isCorrect = random > 30; // 70% chance of being correct
    
    setState(() {
      _lastResult = isCorrect;
      if (isCorrect) _correctCount++;
    });
  }

  void _playCorrectPronunciation() async {
    try {
      setState(() => _isPlaying = true);
      // In a real app, play the correct pronunciation audio
      await Future.delayed(const Duration(seconds: 2));
      setState(() => _isPlaying = false);
    } catch (e) {
      setState(() => _isPlaying = false);
    }
  }

  void _playRecording() async {
    if (_recordedFilePath != null) {
      try {
        await _audioPlayer.play(DeviceFileSource(_recordedFilePath!));
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('خطأ في تشغيل التسجيل')),
          );
        }
      }
    }
  }

  void _nextWord() {
    if (_currentWordIndex < _treatmentWords.length - 1) {
      setState(() {
        _currentWordIndex++;
        _currentTreatmentWord = _treatmentWords[_currentWordIndex];
        _lastResult = null;
        _recordedFilePath = null;
        _attempts = 0;
      });
    }
  }

  void _previousWord() {
    if (_currentWordIndex > 0) {
      setState(() {
        _currentWordIndex--;
        _currentTreatmentWord = _treatmentWords[_currentWordIndex];
        _lastResult = null;
        _recordedFilePath = null;
        _attempts = 0;
      });
    }
  }

  void _showProgress() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(ArabicText.yourProgress),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('${ArabicText.sessionsCompleted}: $_attempts'),
            Text('${ArabicText.accuracy}: ${_attempts > 0 ? (_correctCount / _attempts * 100).toStringAsFixed(1) : 0}%'),
            Text('${ArabicText.wordsLearned}: $_correctCount'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(ArabicText.close),
          ),
        ],
      ),
    );
  }
}
