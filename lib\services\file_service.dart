import 'dart:io';
import 'package:path_provider/path_provider.dart';

class FileService {
  static FileService? _instance;
  static FileService get instance => _instance ??= FileService._();
  
  FileService._();

  /// Get the directory for storing audio recordings
  Future<Directory> getRecordingsDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final recordingsDir = Directory('${appDir.path}/recordings');
    
    if (!await recordingsDir.exists()) {
      await recordingsDir.create(recursive: true);
    }
    
    return recordingsDir;
  }

  /// Get a unique file path for audio recording
  Future<String> getRecordingPath({String prefix = 'recording'}) async {
    final recordingsDir = await getRecordingsDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${recordingsDir.path}/${prefix}_$timestamp.m4a';
  }

  /// Get a file path for treatment recording
  Future<String> getTreatmentRecordingPath() async {
    return await getRecordingPath(prefix: 'treatment');
  }

  /// Get a file path for Quran recording
  Future<String> getQuranRecordingPath() async {
    return await getRecordingPath(prefix: 'quran');
  }

  /// Delete a recording file
  Future<bool> deleteRecording(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      print('Error deleting recording: $e');
      return false;
    }
  }

  /// Get all recording files
  Future<List<File>> getAllRecordings() async {
    try {
      final recordingsDir = await getRecordingsDirectory();
      final files = recordingsDir.listSync()
          .where((entity) => entity is File && entity.path.endsWith('.m4a'))
          .cast<File>()
          .toList();
      
      // Sort by modification date (newest first)
      files.sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));
      
      return files;
    } catch (e) {
      print('Error getting recordings: $e');
      return [];
    }
  }

  /// Clean up old recordings (keep only last 10)
  Future<void> cleanupOldRecordings() async {
    try {
      final recordings = await getAllRecordings();
      if (recordings.length > 10) {
        final toDelete = recordings.skip(10);
        for (final file in toDelete) {
          await file.delete();
        }
      }
    } catch (e) {
      print('Error cleaning up recordings: $e');
    }
  }

  /// Get file size in MB
  Future<double> getFileSize(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        final bytes = await file.length();
        return bytes / (1024 * 1024); // Convert to MB
      }
      return 0.0;
    } catch (e) {
      print('Error getting file size: $e');
      return 0.0;
    }
  }
}
