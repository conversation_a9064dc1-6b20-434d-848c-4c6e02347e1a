import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../models/user.dart';
import '../utils/constants.dart';
import '../widgets/animated_character.dart';
import 'edit_profile_screen.dart';
import 'settings_screen.dart';
import 'login_screen.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الملف الشخصي'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SettingsScreen()),
              );
            },
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppConstants.backgroundGradient,
        ),
        child: Consumer<AuthService>(
          builder: (context, authService, child) {
            final user = authService.currentUser;
            
            if (user == null) {
              return const Center(
                child: Text('لم يتم تسجيل الدخول'),
              );
            }
            
            return SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Column(
                children: [
                  // Profile header
                  _buildProfileHeader(context, user),
                  
                  const SizedBox(height: AppConstants.paddingLarge),
                  
                  // Progress overview
                  _buildProgressOverview(user.progress),
                  
                  const SizedBox(height: AppConstants.paddingLarge),
                  
                  // Menu options
                  _buildMenuOptions(context, authService),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildProfileHeader(BuildContext context, User user) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          children: [
            // Profile picture
            Stack(
              children: [
                CircleAvatar(
                  radius: 50,
                  backgroundColor: AppConstants.primaryColor.withOpacity(0.1),
                  backgroundImage: user.profileImagePath != null
                      ? AssetImage(user.profileImagePath!)
                      : null,
                  child: user.profileImagePath == null
                      ? Icon(
                          user.gender.icon,
                          size: 50,
                          color: AppConstants.primaryColor,
                        )
                      : null,
                ),
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    decoration: BoxDecoration(
                      color: AppConstants.primaryColor,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.edit, color: Colors.white, size: 20),
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const EditProfileScreen(),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            // User info
            Text(
              user.name,
              style: AppConstants.subHeadingStyle,
            ),
            
            const SizedBox(height: AppConstants.paddingSmall),
            
            Text(
              user.email,
              style: AppConstants.captionStyle,
            ),
            
            const SizedBox(height: AppConstants.paddingSmall),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  user.gender.icon,
                  size: 16,
                  color: AppConstants.textSecondaryColor,
                ),
                const SizedBox(width: AppConstants.paddingSmall),
                Text(
                  '${user.gender.arabicName} • ${user.age} سنة',
                  style: AppConstants.captionStyle,
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            // Member since
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingMedium,
                vertical: AppConstants.paddingSmall,
              ),
              decoration: BoxDecoration(
                color: AppConstants.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              ),
              child: Text(
                'عضو منذ ${_formatDate(user.createdAt)}',
                style: AppConstants.captionStyle.copyWith(
                  color: AppConstants.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressOverview(UserProgress progress) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إحصائيات التقدم',
              style: AppConstants.subHeadingStyle,
            ),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            // Progress stats
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'الجلسات',
                    progress.totalSessions.toString(),
                    Icons.play_circle_filled,
                    AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: _buildStatCard(
                    'التمارين',
                    progress.completedExercises.toString(),
                    Icons.assignment_turned_in,
                    AppConstants.secondaryColor,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'أيام متتالية',
                    progress.streakDays.toString(),
                    Icons.local_fire_department,
                    AppConstants.accentColor,
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: _buildStatCard(
                    'الدروس',
                    progress.completedLessons.length.toString(),
                    Icons.menu_book,
                    const Color(0xFF8BC34A),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            // Last session
            Container(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                color: AppConstants.backgroundColor,
                borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              ),
              child: Row(
                children: [
                  const Icon(Icons.schedule, color: AppConstants.textSecondaryColor),
                  const SizedBox(width: AppConstants.paddingSmall),
                  Text(
                    'آخر جلسة: ${_formatDate(progress.lastSessionDate)}',
                    style: AppConstants.captionStyle,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            value,
            style: AppConstants.subHeadingStyle.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: AppConstants.captionStyle,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMenuOptions(BuildContext context, AuthService authService) {
    final menuItems = [
      {
        'title': 'تعديل الملف الشخصي',
        'subtitle': 'تحديث المعلومات الشخصية',
        'icon': Icons.edit,
        'onTap': () => Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const EditProfileScreen()),
        ),
      },
      {
        'title': 'الإعدادات',
        'subtitle': 'تخصيص التطبيق حسب تفضيلاتك',
        'icon': Icons.settings,
        'onTap': () => Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const SettingsScreen()),
        ),
      },
      {
        'title': 'تغيير كلمة المرور',
        'subtitle': 'تحديث كلمة المرور الخاصة بك',
        'icon': Icons.lock,
        'onTap': () => _showChangePasswordDialog(context, authService),
      },
      {
        'title': 'مشاركة التطبيق',
        'subtitle': 'شارك التطبيق مع الأصدقاء',
        'icon': Icons.share,
        'onTap': () => _shareApp(context),
      },
      {
        'title': 'تسجيل الخروج',
        'subtitle': 'الخروج من الحساب الحالي',
        'icon': Icons.logout,
        'onTap': () => _showLogoutDialog(context, authService),
      },
    ];

    return Column(
      children: menuItems.map((item) {
        return Card(
          margin: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
          child: ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppConstants.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              ),
              child: Icon(
                item['icon'] as IconData,
                color: AppConstants.primaryColor,
              ),
            ),
            title: Text(
              item['title'] as String,
              style: AppConstants.bodyStyle.copyWith(fontWeight: FontWeight.w600),
            ),
            subtitle: Text(
              item['subtitle'] as String,
              style: AppConstants.captionStyle,
            ),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: item['onTap'] as VoidCallback,
          ),
        );
      }).toList(),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;
    
    if (difference == 0) {
      return 'اليوم';
    } else if (difference == 1) {
      return 'أمس';
    } else if (difference < 7) {
      return 'منذ $difference أيام';
    } else if (difference < 30) {
      final weeks = (difference / 7).floor();
      return 'منذ $weeks أسبوع';
    } else if (difference < 365) {
      final months = (difference / 30).floor();
      return 'منذ $months شهر';
    } else {
      final years = (difference / 365).floor();
      return 'منذ $years سنة';
    }
  }

  void _showChangePasswordDialog(BuildContext context, AuthService authService) {
    final currentPasswordController = TextEditingController();
    final newPasswordController = TextEditingController();
    final confirmPasswordController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تغيير كلمة المرور'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: currentPasswordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'كلمة المرور الحالية',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            TextField(
              controller: newPasswordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'كلمة المرور الجديدة',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            TextField(
              controller: confirmPasswordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'تأكيد كلمة المرور الجديدة',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (newPasswordController.text == confirmPasswordController.text &&
                  newPasswordController.text.length >= 6) {
                final success = await authService.changePassword(
                  currentPasswordController.text,
                  newPasswordController.text,
                );
                
                if (context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(success ? 'تم تغيير كلمة المرور بنجاح' : 'خطأ في تغيير كلمة المرور'),
                      backgroundColor: success ? AppConstants.successColor : AppConstants.errorColor,
                    ),
                  );
                }
              }
            },
            child: const Text('تغيير'),
          ),
        ],
      ),
    );
  }

  void _shareApp(BuildContext context) {
    // In a real app, you would use the share package
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('مشاركة التطبيق...'),
        backgroundColor: AppConstants.primaryColor,
      ),
    );
  }

  void _showLogoutDialog(BuildContext context, AuthService authService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              await authService.logout();
              if (context.mounted) {
                Navigator.pushAndRemoveUntil(
                  context,
                  MaterialPageRoute(builder: (context) => const LoginScreen()),
                  (route) => false,
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppConstants.errorColor),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }
}
