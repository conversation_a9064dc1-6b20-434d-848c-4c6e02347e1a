class TreatmentSession {
  final String id;
  final String disorderId;
  final String word;
  final String userRecordingPath;
  final String correctPronunciationPath;
  final double accuracyScore;
  final bool isCorrect;
  final DateTime timestamp;
  final int attempts;

  TreatmentSession({
    required this.id,
    required this.disorderId,
    required this.word,
    required this.userRecordingPath,
    required this.correctPronunciationPath,
    required this.accuracyScore,
    required this.isCorrect,
    required this.timestamp,
    required this.attempts,
  });
}

class UserProgress {
  final String userId;
  final String disorderId;
  final int totalSessions;
  final int correctPronunciations;
  final double averageAccuracy;
  final List<String> masteredWords;
  final List<String> difficultWords;
  final DateTime lastSessionDate;

  UserProgress({
    required this.userId,
    required this.disorderId,
    required this.totalSessions,
    required this.correctPronunciations,
    required this.averageAccuracy,
    required this.masteredWords,
    required this.difficultWords,
    required this.lastSessionDate,
  });

  double get successRate => totalSessions > 0 ? correctPronunciations / totalSessions : 0.0;
}
