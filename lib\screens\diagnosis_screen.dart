import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:video_player/video_player.dart';
import '../models/speech_disorder.dart';
import '../utils/constants.dart';
import '../utils/arabic_text.dart';
import '../widgets/animated_character.dart';

class DiagnosisScreen extends StatefulWidget {
  const DiagnosisScreen({super.key});

  @override
  State<DiagnosisScreen> createState() => _DiagnosisScreenState();
}

class _DiagnosisScreenState extends State<DiagnosisScreen> {
  final AudioPlayer _audioPlayer = AudioPlayer();
  SpeechDisorder? _selectedDisorder;
  bool _isPlaying = false;
  VideoPlayerController? _videoController;

  static const Map<String, String> _videoPaths = {
    'stuttering': 'assets/videos/stuttering.mp4',
    'lisp': 'assets/videos/lisp.mp4',
    'selective_mutism': 'assets/videos/selective_mutism.mp4',
  };

  @override
  void dispose() {
    _audioPlayer.dispose();
    _videoController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
      appBar: AppBar(
        title: Text(ArabicText.diagnosisTitle),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppConstants.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Character and instruction
              _buildHeader(),
              
              // Disorders list
              Expanded(
                child: _selectedDisorder == null
                    ? _buildDisordersList()
                    : _buildDisorderDetails(),
              ),
            ],
          ),
        ),
      ),
    ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        children: [
          if (_selectedDisorder == null)
            AnimatedCharacter(
              size: 200,
              message: ArabicText.selectDisorder,
            )
          else if (_videoController != null && _videoController!.value.isInitialized)
            Column(
              children: [
                GestureDetector(
                  onTap: () {
                    if (_videoController!.value.isPlaying) {
                      _videoController?.pause();
                    } else {
                      _videoController?.play();
                    }
                  },
                  child: AspectRatio(
                    aspectRatio: _videoController!.value.aspectRatio,
                    child: VideoPlayer(_videoController!),
                  ),
                ),
                const SizedBox(height: AppConstants.paddingMedium),
                Text(
                  '📚 تعرف على ${_selectedDisorder!.nameArabic}',
                  style: AppConstants.subHeadingStyle,
                  textAlign: TextAlign.center,
                ),
              ],
            )
          else
            const SizedBox(
              height: 200,
              child: Center(child: CircularProgressIndicator()),
            ),

          const SizedBox(height: AppConstants.paddingMedium),
          if (_selectedDisorder == null)
            Text(
              ArabicText.chooseService,
              style: AppConstants.subHeadingStyle,
              textAlign: TextAlign.center,
            ),
        ],
      ),
    );
  }

  Widget _buildDisordersList() {
    final disorders = SpeechDisorder.getAllDisorders();
    
    return AnimationLimiter(
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        itemCount: disorders.length,
        itemBuilder: (context, index) {
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: AppConstants.animationMedium,
            child: SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(
                child: _buildDisorderCard(disorders[index]),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDisorderCard(SpeechDisorder disorder) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: Card(
        child: InkWell(
          onTap: () => _selectDisorder(disorder),
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          child: Container(
            padding: const EdgeInsets.all(AppConstants.paddingLarge),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(AppConstants.paddingMedium),
                      decoration: BoxDecoration(
                        color: AppConstants.primaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                      ),
                      child: Icon(
                        _getDisorderIcon(disorder.id),
                        color: AppConstants.primaryColor,
                        size: 32,
                      ),
                    ),
                    const SizedBox(width: AppConstants.paddingMedium),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            disorder.nameArabic,
                            style: AppConstants.subHeadingStyle,
                          ),
                          const SizedBox(height: AppConstants.paddingSmall),
                          Text(
                            disorder.descriptionArabic,
                            style: AppConstants.captionStyle,
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      color: AppConstants.textSecondaryColor,
                      size: 16,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDisorderDetails() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Back button
          Row(
            children: [
              IconButton(
                onPressed: _unselectDisorder,
                icon: const Icon(Icons.arrow_back),
              ),
              Expanded(
                child: Text(
                  _selectedDisorder!.nameArabic,
                  style: AppConstants.headingStyle,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.paddingMedium),
          
          // Audio explanation
          _buildAudioSection(),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          // Explanation text
          _buildExplanationSection(),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          // Symptoms
          _buildSymptomsSection(),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          // Treatment words preview
          _buildTreatmentWordsSection(),
        ],
      ),
    );
  }

  Widget _buildAudioSection() {
    return Card(
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              ArabicText.listenExplanation,
              style: AppConstants.subHeadingStyle,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isPlaying ? _stopAudio : _playAudio,
                    icon: Icon(_isPlaying ? Icons.stop : Icons.play_arrow),
                    label: Text(_isPlaying ? 'إيقاف' : 'تشغيل'),
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                if (_isPlaying)
                  const CircularProgressIndicator(),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExplanationSection() {
    return Card(
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الشرح التفصيلي',
              style: AppConstants.subHeadingStyle,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              _selectedDisorder!.explanationArabic,
              style: AppConstants.bodyStyle.copyWith(height: 1.6),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSymptomsSection() {
    return Card(
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              ArabicText.symptoms,
              style: AppConstants.subHeadingStyle,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            ...(_selectedDisorder!.symptoms.map((symptom) => Padding(
              padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: AppConstants.primaryColor,
                    size: 20,
                  ),
                  const SizedBox(width: AppConstants.paddingSmall),
                  Expanded(
                    child: Text(
                      symptom,
                      style: AppConstants.bodyStyle,
                    ),
                  ),
                ],
              ),
            ))),
          ],
        ),
      ),
    );
  }

  Widget _buildTreatmentWordsSection() {
    return Card(
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'كلمات التدريب',
              style: AppConstants.subHeadingStyle,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Wrap(
              spacing: AppConstants.paddingSmall,
              runSpacing: AppConstants.paddingSmall,
              children: _selectedDisorder!.treatmentWords.take(5).map((word) => 
                Chip(
                  label: Text(word),
                  backgroundColor: AppConstants.primaryColor.withValues(alpha: 0.1),
                ),
              ).toList(),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            ElevatedButton(
              onPressed: () {
                // Navigate to treatment screen with this disorder
                Navigator.pop(context);
                // TODO: Navigate to treatment screen
              },
              child: const Text('ابدأ التدريب'),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getDisorderIcon(String disorderId) {
    switch (disorderId) {
      case 'stuttering':
        return Icons.record_voice_over;
      case 'lisp':
        return Icons.hearing;
      case 'selective_mutism':
        return Icons.voice_over_off;
      default:
        return Icons.medical_services;
    }
  }

  void _selectDisorder(SpeechDisorder disorder) {
    setState(() {
      _selectedDisorder = disorder;
    });
    _initializeVideoPlayer(disorder.id);
  }

  void _unselectDisorder() {
    setState(() {
      _selectedDisorder = null;
      _videoController?.dispose();
      _videoController = null;
    });
  }

  Future<void> _initializeVideoPlayer(String disorderId) async {
    final videoPath = _videoPaths[disorderId];
    if (videoPath == null) return;

    _videoController?.dispose();
    _videoController = VideoPlayerController.asset(videoPath)
      ..initialize().then((_) {
        setState(() {});
        _videoController?.play();
        _videoController?.setLooping(true);
      });
  }

  void _playAudio() async {
    try {
      setState(() => _isPlaying = true);
      // In a real app, you would play the actual audio file
      // await _audioPlayer.play(AssetSource(_selectedDisorder!.audioExplanationPath));
      
      // Simulate audio playback
      await Future.delayed(const Duration(seconds: 3));
      setState(() => _isPlaying = false);
    } catch (e) {
      setState(() => _isPlaying = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('خطأ في تشغيل الصوت')),
        );
      }
    }
  }

  void _stopAudio() {
    _audioPlayer.stop();
    setState(() => _isPlaying = false);
  }
}
