import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../utils/constants.dart';
import '../utils/arabic_text.dart';
import '../widgets/animated_character.dart';
import 'diagnosis_screen.dart';
import 'treatment_screen.dart';
import 'specialists_screen.dart';
import 'quran_learning_screen.dart';
import 'profile_screen.dart';
import 'parents_education_screen.dart';
import '../widgets/ai_chatbot.dart';
import 'ai_assistant_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: AppConstants.animationSlow,
      vsync: this,
    );
    _slideController = AnimationController(
      duration: AppConstants.animationMedium,
      vsync: this,
    );

    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            'نُطقك مهم',
            style: AppConstants.headingStyle.copyWith(
              color: AppConstants.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          centerTitle: true,
          backgroundColor: Colors.transparent,
          elevation: 0,
          actions: [
            IconButton(
              icon: const Icon(Icons.person),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ProfileScreen(),
                  ),
                );
              },
            ),
          ],
        ),
        body: Container(
          decoration: const BoxDecoration(
            gradient: AppConstants.backgroundGradient,
          ),
          child: SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.paddingLarge),
              child: Column(
                children: [
                  // App Title and Slogan
                  _buildHeader(),

                  const SizedBox(height: AppConstants.paddingXLarge),

                  // Animated Character
                  _buildAnimatedCharacter(),

                  const SizedBox(height: AppConstants.paddingXLarge),

                  // Service Cards
                  _buildServiceCards(),

                  const SizedBox(height: AppConstants.paddingLarge),

                  // Motivational Quote
                  _buildMotivationalQuote(),

                  // زر مساعد فصيح الذكي
                  const SizedBox(height: AppConstants.paddingLarge),
                  ElevatedButton.icon(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.deepPurple,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        vertical: 16,
                        horizontal: 32,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                      elevation: 4,
                    ),
                    icon: const Icon(Icons.smart_toy_rounded, size: 28),
                    label: const Text(
                      'مساعد فصيح الذكي',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const AIAssistantScreen(),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return FadeTransition(
      opacity: _fadeController,
      child: Column(
        children: [
          Text(
            ArabicText.appTitle,
            style: AppConstants.headingStyle.copyWith(
              fontSize: 32,
              color: AppConstants.primaryColor,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            ArabicText.welcome,
            style: AppConstants.subHeadingStyle.copyWith(
              color: AppConstants.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedCharacter() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppConstants.primaryColor.withValues(alpha: 0.05),
            AppConstants.secondaryColor.withValues(alpha: 0.05),
            Colors.white.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge * 2),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryColor.withValues(alpha: 0.1),
            blurRadius: 30,
            offset: const Offset(0, 10),
            spreadRadius: 5,
          ),
        ],
        border: Border.all(
          color: AppConstants.primaryColor.withValues(alpha: 0.1),
          width: 2,
        ),
      ),
      child: FadeTransition(
        opacity: _fadeController,
        child: SlideTransition(
          position: Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero)
              .animate(
                CurvedAnimation(
                  parent: _slideController,
                  curve: Curves.easeOutBack,
                ),
              ),
          child: const AnimatedCharacter(
            size: 220,
            message: ArabicText.characterWelcome,
          ),
        ),
      ),
    );
  }

  Widget _buildServiceCards() {
    final services = [
      {
        'title': ArabicText.startDiagnosis,
        'subtitle': 'تعرف على نوع اضطراب النطق',
        'icon': Icons.medical_services,
        'color': AppConstants.primaryColor,
        'gradient': [AppConstants.primaryColor, const Color(0xFF66BB6A)],
        'onTap': () => _navigateToScreen(const DiagnosisScreen()),
      },
      {
        'title': ArabicText.startTreatment,
        'subtitle': 'ابدأ جلسات التدريب والعلاج',
        'icon': Icons.mic,
        'color': AppConstants.secondaryColor,
        'gradient': [AppConstants.secondaryColor, const Color(0xFF42A5F5)],
        'onTap': () => _navigateToScreen(const TreatmentScreen()),
      },
      {
        'title': 'تعلم القرآن الكريم',
        'subtitle': 'تعلم تلاوة القرآن بطريقة تفاعلية',
        'icon': Icons.menu_book,
        'color': const Color(0xFF8BC34A),
        'gradient': [const Color(0xFF8BC34A), const Color(0xFFAED581)],
        'onTap': () => _navigateToScreen(const QuranLearningScreen()),
      },
      {
        'title': ArabicText.findSpecialist,
        'subtitle': 'تواصل مع أخصائيي النطق',
        'icon': Icons.people,
        'color': AppConstants.accentColor,
        'gradient': [AppConstants.accentColor, const Color(0xFFFFB74D)],
        'onTap': () => _navigateToScreen(const SpecialistsScreen()),
      },
      {
        'title': 'دليل الآباء التعليمي',
        'subtitle': 'فيديوهات ومقالات ومواقع الأطباء',
        'icon': Icons.school,
        'color': const Color(0xFF9C27B0),
        'gradient': [const Color(0xFF9C27B0), const Color(0xFFBA68C8)],
        'onTap': () => _navigateToScreen(const ParentsEducationScreen()),
      },
    ];

    return AnimationLimiter(
      child: Column(
        children: AnimationConfiguration.toStaggeredList(
          duration: AppConstants.animationMedium,
          childAnimationBuilder: (widget) => SlideAnimation(
            horizontalOffset: 50.0,
            child: FadeInAnimation(child: widget),
          ),
          children: services
              .map((service) => _buildServiceCard(service))
              .toList(),
        ),
      ),
    );
  }

  Widget _buildServiceCard(Map<String, dynamic> service) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: Card(
        child: InkWell(
          onTap: service['onTap'],
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          child: Container(
            padding: const EdgeInsets.all(AppConstants.paddingLarge),
            child: Row(
              children: [
                // Enhanced icon container
                Container(
                  width: 70,
                  height: 70,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: service['gradient'],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(
                      AppConstants.radiusLarge,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: service['color'].withValues(alpha: 0.4),
                        blurRadius: 12,
                        offset: const Offset(0, 6),
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                  child: Stack(
                    children: [
                      Center(
                        child: Icon(
                          service['icon'],
                          color: Colors.white,
                          size: 32,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: AppConstants.paddingLarge),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        service['title'],
                        style: AppConstants.subHeadingStyle.copyWith(
                          fontSize: 17,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: AppConstants.paddingSmall),
                      Text(
                        service['subtitle'],
                        style: AppConstants.captionStyle.copyWith(
                          fontSize: 13,
                          height: 1.3,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: service['color'].withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(
                      AppConstants.radiusSmall,
                    ),
                  ),
                  child: Icon(
                    Icons.arrow_back_ios,
                    color: service['color'],
                    size: 16,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMotivationalQuote() {
    return FadeTransition(
      opacity: _fadeController,
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        decoration: BoxDecoration(
          gradient: AppConstants.primaryGradient,
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          boxShadow: [
            BoxShadow(
              color: AppConstants.primaryColor.withValues(alpha: 0.3),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Text(
          ArabicText.appSlogan,
          style: AppConstants.bodyStyle.copyWith(
            color: Colors.white,
            height: 1.6,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  void _navigateToScreen(Widget screen) {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => screen,
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.ease;

          var tween = Tween(
            begin: begin,
            end: end,
          ).chain(CurveTween(curve: curve));

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
      ),
    );
  }
}
