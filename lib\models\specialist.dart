class Specialist {
  final String id;
  final String fullName;
  final String specialization;
  final String phoneNumber;
  final String email;
  final String location;
  final String state;
  final String workingHours;
  final double rating;
  final int experienceYears;
  final String profileImagePath;
  final bool isAvailable;
  final List<String> languages;
  final String clinicAddress;

  Specialist({
    required this.id,
    required this.fullName,
    required this.specialization,
    required this.phoneNumber,
    required this.email,
    required this.location,
    required this.state,
    required this.workingHours,
    required this.rating,
    required this.experienceYears,
    required this.profileImagePath,
    required this.isAvailable,
    required this.languages,
    required this.clinicAddress,
  });

  static List<Specialist> getAllSpecialists() {
    return [
      Specialist(
        id: '1',
        fullName: 'د. فاطمة أحمد محمد',
        specialization: 'أخصائية علاج النطق واللغة',
        phoneNumber: '+966501234567',
        email: '<EMAIL>',
        location: 'الرياض',
        state: 'المملكة العربية السعودية',
        workingHours: 'الأحد - الخميس: 9:00 ص - 5:00 م',
        rating: 4.8,
        experienceYears: 12,
        profileImagePath: 'assets/images/dr_fatima.jpg',
        isAvailable: true,
        languages: ['العربية', 'الإنجليزية'],
        clinicAddress: 'شارع الملك فهد، حي العليا، الرياض',
      ),
      Specialist(
        id: '2',
        fullName: 'د. محمد عبدالله السالم',
        specialization: 'أخصائي اضطرابات النطق عند الأطفال',
        phoneNumber: '+966502345678',
        email: '<EMAIL>',
        location: 'جدة',
        state: 'المملكة العربية السعودية',
        workingHours: 'السبت - الأربعاء: 10:00 ص - 6:00 م',
        rating: 4.9,
        experienceYears: 15,
        profileImagePath: 'assets/images/dr_mohammed.jpg',
        isAvailable: true,
        languages: ['العربية'],
        clinicAddress: 'طريق الملك عبدالعزيز، حي الروضة، جدة',
      ),
      Specialist(
        id: '3',
        fullName: 'د. عائشة حسن الزهراني',
        specialization: 'أخصائية التلعثم والطلاقة الكلامية',
        phoneNumber: '+966503456789',
        email: '<EMAIL>',
        location: 'الدمام',
        state: 'المملكة العربية السعودية',
        workingHours: 'الأحد - الخميس: 8:00 ص - 4:00 م',
        rating: 4.7,
        experienceYears: 10,
        profileImagePath: 'assets/images/dr_aisha.jpg',
        isAvailable: false,
        languages: ['العربية', 'الإنجليزية', 'الفرنسية'],
        clinicAddress: 'شارع الأمير محمد بن فهد، حي الفيصلية، الدمام',
      ),
      Specialist(
        id: '4',
        fullName: 'د. خالد أحمد الغامدي',
        specialization: 'أخصائي علاج اللثغة وتقويم النطق',
        phoneNumber: '+966504567890',
        email: '<EMAIL>',
        location: 'مكة المكرمة',
        state: 'المملكة العربية السعودية',
        workingHours: 'السبت - الأربعاء: 9:00 ص - 5:00 م',
        rating: 4.6,
        experienceYears: 8,
        profileImagePath: 'assets/images/dr_khalid.jpg',
        isAvailable: true,
        languages: ['العربية'],
        clinicAddress: 'شارع إبراهيم الخليل، حي العزيزية، مكة المكرمة',
      ),
      Specialist(
        id: '5',
        fullName: 'د. نورا سعد العتيبي',
        specialization: 'أخصائية البكم الانتقائي والقلق الكلامي',
        phoneNumber: '+966505678901',
        email: '<EMAIL>',
        location: 'المدينة المنورة',
        state: 'المملكة العربية السعودية',
        workingHours: 'الأحد - الخميس: 10:00 ص - 6:00 م',
        rating: 4.9,
        experienceYears: 14,
        profileImagePath: 'assets/images/dr_nora.jpg',
        isAvailable: true,
        languages: ['العربية', 'الإنجليزية'],
        clinicAddress: 'طريق قباء، حي قربان، المدينة المنورة',
      ),
    ];
  }
}

class Appointment {
  final String id;
  final String specialistId;
  final DateTime dateTime;
  final String patientName;
  final String contactNumber;
  final String notes;
  final AppointmentStatus status;

  Appointment({
    required this.id,
    required this.specialistId,
    required this.dateTime,
    required this.patientName,
    required this.contactNumber,
    required this.notes,
    required this.status,
  });
}

enum AppointmentStatus {
  pending,
  confirmed,
  completed,
  cancelled,
}
