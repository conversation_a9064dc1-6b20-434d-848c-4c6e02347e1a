# نُطقك مهم - تعليمات التشغيل والعرض التوضيحي

## نظرة عامة على التطبيق

تم إنشاء تطبيق Flutter شامل لعلاج النطق للأطفال باللغة العربية مع الميزات التالية:

### ✅ الميزات المُنجزة

#### 1. الشاشة الرئيسية (HomeScreen)
- **شخصية كرتونية متحركة** مع رسوم متحركة للعيون والحركة
- **رسالة تحفيزية** باللغة العربية
- **ثلاث بطاقات خدمة** رئيسية:
  - التشخيص
  - العلاج
  - الأخصائيين
- **انتقالات سلسة** بين الشاشات
- **تصميم متجاوب** مع دعم RTL

#### 2. شاشة التشخيص (DiagnosisScreen)
- **ثلاثة أنواع من اضطرابات النطق**:
  - التلعثم (Stuttering)
  - اللثغة (Lisp)
  - البكم الانتقائي (Selective Mutism)
- **شرح تفصيلي** لكل اضطراب
- **قائمة الأعراض** لكل اضطراب
- **كلمات التدريب** المخصصة
- **تشغيل الصوت** (محاكاة)
- **واجهة تفاعلية** مع الشخصية المتحركة

#### 3. شاشة العلاج (TreatmentScreen)
- **تسجيل الصوت** باستخدام مكتبة record
- **مقارنة النطق** (محاكاة خوارزمية)
- **تقييم بصري** (✅ صحيح / ❌ خطأ)
- **تتبع المحاولات** والنتائج
- **كلمات تدريب متنوعة** لكل اضطراب
- **تشغيل النطق الصحيح** (محاكاة)
- **شخصية متحركة تفاعلية** تقدم التشجيع

#### 4. شاشة الأخصائيين (SpecialistsScreen)
- **قاعدة بيانات شاملة** من أخصائيي علاج النطق
- **معلومات تفصيلية**:
  - الاسم الكامل
  - التخصص
  - رقم الهاتف
  - الإيميل
  - الموقع والعنوان
  - ساعات العمل
  - التقييم
  - سنوات الخبرة
- **وظائف التواصل**:
  - الاتصال المباشر
  - إرسال إيميل
  - حجز موعد
- **البحث والتصفية** حسب المدينة
- **واجهة حجز المواعيد** التفاعلية

#### 5. المكونات المخصصة
- **AnimatedCharacter**: شخصية كرتونية متحركة مع تعبيرات وجه
- **AudioRecorder**: مسجل صوت مع رسوم متحركة
- **PronunciationFeedback**: تقييم النطق مع تشجيع
- **SpecialistCard**: بطاقة أخصائي مع جميع المعلومات

#### 6. التصميم والواجهة
- **دعم كامل للغة العربية** مع RTL
- **خطوط عربية** (Cairo font)
- **ألوان متناسقة** مناسبة للأطفال
- **رسوم متحركة سلسة** في جميع أنحاء التطبيق
- **تصميم متجاوب** لجميع أحجام الشاشات

## التشغيل والاختبار

### المتطلبات
```bash
Flutter SDK 3.8.1+
Dart 3.0+
Android Studio / VS Code
```

### خطوات التشغيل

1. **تثبيت التبعيات**:
```bash
flutter pub get
```

2. **تشغيل التطبيق**:
```bash
flutter run
```

3. **بناء APK للاختبار**:
```bash
flutter build apk --debug
```

### اختبار الميزات

#### الشاشة الرئيسية
1. افتح التطبيق
2. لاحظ الشخصية المتحركة مع الرسوم المتحركة
3. اقرأ الرسالة التحفيزية
4. اضغط على كل بطاقة خدمة للانتقال

#### شاشة التشخيص
1. اضغط على "ابدأ التشخيص"
2. اختر أحد اضطرابات النطق الثلاثة
3. اقرأ الشرح التفصيلي
4. استعرض قائمة الأعراض
5. اضغط على "تشغيل الشرح" (محاكاة)
6. اضغط على "ابدأ التدريب" للانتقال للعلاج

#### شاشة العلاج
1. اختر نوع الاضطراب
2. استمع للنطق الصحيح
3. اضغط على زر التسجيل لبدء تسجيل صوتك
4. اضغط مرة أخرى لإيقاف التسجيل
5. شاهد النتيجة (صحيح/خطأ)
6. لاحظ تفاعل الشخصية المتحركة
7. انتقل بين الكلمات المختلفة

#### شاشة الأخصائيين
1. اضغط على "ابحث عن أخصائي"
2. استعرض قائمة الأخصائيين
3. استخدم البحث للعثور على أخصائي معين
4. صفي حسب المدينة
5. اضغط على "اتصل الآن" لفتح تطبيق الهاتف
6. اضغط على "أرسل إيميل" لفتح تطبيق البريد
7. اضغط على "احجز موعد" لفتح نموذج الحجز
8. املأ نموذج الحجز واضغط "احجز موعد"

## الملاحظات التقنية

### الميزات المحاكاة
- **تشغيل الصوت**: يتم محاكاة تشغيل الملفات الصوتية
- **تحليل النطق**: يتم محاكاة خوارزمية تحليل النطق
- **الخطوط العربية**: ملفات خطوط فارغة (تحتاج لخطوط حقيقية)

### التحسينات المستقبلية
1. **إضافة ملفات صوتية حقيقية** للنطق الصحيح
2. **تطبيق خوارزميات تحليل النطق** الحقيقية
3. **إضافة قاعدة بيانات** للمستخدمين والتقدم
4. **تطبيق نظام إشعارات** للمواعيد
5. **إضافة ألعاب تعليمية** تفاعلية
6. **تطبيق نظام مكافآت** للتحفيز

## الهيكل التقني

### الملفات الرئيسية
- `lib/main.dart` - نقطة البداية
- `lib/screens/` - جميع الشاشات
- `lib/widgets/` - المكونات المخصصة
- `lib/models/` - نماذج البيانات
- `lib/utils/` - الأدوات والثوابت

### التبعيات المستخدمة
- `record` - تسجيل الصوت
- `audioplayers` - تشغيل الصوت
- `permission_handler` - إدارة الأذونات
- `url_launcher` - فتح الروابط
- `flutter_staggered_animations` - الرسوم المتحركة

## الخلاصة

تم إنشاء تطبيق شامل ومتكامل لعلاج النطق للأطفال باللغة العربية مع جميع الميزات المطلوبة. التطبيق جاهز للاختبار والتطوير الإضافي.
