import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../models/user.dart';
import '../utils/constants.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  late UserPreferences _preferences;

  @override
  void initState() {
    super.initState();
    final user = Provider.of<AuthService>(context, listen: false).currentUser;
    _preferences = user?.preferences ?? UserPreferences.defaultPreferences();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppConstants.backgroundGradient,
        ),
        child: ListView(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          children: [
            // Notifications section
            _buildNotificationsSection(),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            // Audio & Visual section
            _buildAudioVisualSection(),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            // Learning preferences section
            _buildLearningSection(),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            // App preferences section
            _buildAppSection(),
            
            const SizedBox(height: AppConstants.paddingLarge),
            
            // Save button
            _buildSaveButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الإشعارات',
              style: AppConstants.subHeadingStyle,
            ),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            SwitchListTile(
              title: const Text('تفعيل الإشعارات'),
              subtitle: const Text('تلقي تذكيرات الجلسات والتحديثات'),
              value: _preferences.notificationsEnabled,
              onChanged: (value) {
                setState(() {
                  _preferences = _preferences.copyWith(notificationsEnabled: value);
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAudioVisualSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الصوت والمرئيات',
              style: AppConstants.subHeadingStyle,
            ),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            SwitchListTile(
              title: const Text('تفعيل الأصوات'),
              subtitle: const Text('تشغيل الأصوات والتأثيرات الصوتية'),
              value: _preferences.soundEnabled,
              onChanged: (value) {
                setState(() {
                  _preferences = _preferences.copyWith(soundEnabled: value);
                });
              },
            ),
            
            SwitchListTile(
              title: const Text('تفعيل الاهتزاز'),
              subtitle: const Text('اهتزاز الجهاز عند التفاعل'),
              value: _preferences.vibrationEnabled,
              onChanged: (value) {
                setState(() {
                  _preferences = _preferences.copyWith(vibrationEnabled: value);
                });
              },
            ),
            
            const Divider(),
            
            ListTile(
              title: const Text('حجم الخط'),
              subtitle: Text('الحجم الحالي: ${_preferences.fontSize.toInt()}'),
              trailing: SizedBox(
                width: 150,
                child: Slider(
                  value: _preferences.fontSize,
                  min: 12.0,
                  max: 24.0,
                  divisions: 6,
                  label: _preferences.fontSize.toInt().toString(),
                  onChanged: (value) {
                    setState(() {
                      _preferences = _preferences.copyWith(fontSize: value);
                    });
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLearningSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفضيلات التعلم',
              style: AppConstants.subHeadingStyle,
            ),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            SwitchListTile(
              title: const Text('إظهار النطق بالأحرف اللاتينية'),
              subtitle: const Text('عرض النطق الصوتي للكلمات العربية'),
              value: _preferences.showTransliteration,
              onChanged: (value) {
                setState(() {
                  _preferences = _preferences.copyWith(showTransliteration: value);
                });
              },
            ),
            
            SwitchListTile(
              title: const Text('تشغيل الصوت تلقائياً'),
              subtitle: const Text('تشغيل الصوت عند عرض النصوص'),
              value: _preferences.autoPlayAudio,
              onChanged: (value) {
                setState(() {
                  _preferences = _preferences.copyWith(autoPlayAudio: value);
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات التطبيق',
              style: AppConstants.subHeadingStyle,
            ),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            ListTile(
              title: const Text('اللغة'),
              subtitle: Text(_preferences.language == 'ar' ? 'العربية' : 'English'),
              trailing: DropdownButton<String>(
                value: _preferences.language,
                items: const [
                  DropdownMenuItem(value: 'ar', child: Text('العربية')),
                  DropdownMenuItem(value: 'en', child: Text('English')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _preferences = _preferences.copyWith(language: value);
                    });
                  }
                },
              ),
            ),
            
            ListTile(
              title: const Text('المظهر'),
              subtitle: Text(_getThemeName(_preferences.themeMode)),
              trailing: DropdownButton<ThemeMode>(
                value: _preferences.themeMode,
                items: const [
                  DropdownMenuItem(value: ThemeMode.system, child: Text('تلقائي')),
                  DropdownMenuItem(value: ThemeMode.light, child: Text('فاتح')),
                  DropdownMenuItem(value: ThemeMode.dark, child: Text('داكن')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _preferences = _preferences.copyWith(themeMode: value);
                    });
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return Consumer<AuthService>(
      builder: (context, authService, child) {
        return ElevatedButton(
          onPressed: authService.isLoading ? null : _saveSettings,
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            backgroundColor: AppConstants.primaryColor,
          ),
          child: authService.isLoading
              ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text(
                  'حفظ الإعدادات',
                  style: TextStyle(fontSize: 16),
                ),
        );
      },
    );
  }

  String _getThemeName(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.system:
        return 'تلقائي';
      case ThemeMode.light:
        return 'فاتح';
      case ThemeMode.dark:
        return 'داكن';
    }
  }

  void _saveSettings() async {
    final authService = Provider.of<AuthService>(context, listen: false);
    
    final success = await authService.updatePreferences(_preferences);
    
    if (mounted) {
      if (success) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ الإعدادات بنجاح'),
            backgroundColor: AppConstants.successColor,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('خطأ في حفظ الإعدادات'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }
}
