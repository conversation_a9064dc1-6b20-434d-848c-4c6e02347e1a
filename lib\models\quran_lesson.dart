import 'package:flutter/material.dart';

class QuranLesson {
  final String id;
  final String surahName;
  final String surahNameArabic;
  final int surahNumber;
  final List<QuranVerse> verses;
  final String audioPath;
  final String description;
  final DifficultyLevel difficulty;
  final String category;

  QuranLesson({
    required this.id,
    required this.surahName,
    required this.surahNameArabic,
    required this.surahNumber,
    required this.verses,
    required this.audioPath,
    required this.description,
    required this.difficulty,
    required this.category,
  });

  static List<QuranLesson> getAllLessons() {
    return [
      QuranLesson(
        id: 'fatiha',
        surahName: 'Al-Fatiha',
        surahNameArabic: 'الفاتحة',
        surahNumber: 1,
        verses: [
          QuranVerse(
            number: 1,
            arabicText: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
            transliteration: 'Bismillahi ar-rahmani ar-raheem',
            translation: 'بسم الله الرحمن الرحيم',
            audioPath: 'assets/audio/quran/fatiha_1.mp3',
          ),
          QuranVerse(
            number: 2,
            arabicText: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
            transliteration: '<PERSON><PERSON><PERSON> lilla<PERSON> rabbi al-al<PERSON>',
            translation: 'الحمد لله رب العالمين',
            audioPath: 'assets/audio/quran/fatiha_2.mp3',
          ),
          QuranVerse(
            number: 3,
            arabicText: 'الرَّحْمَٰنِ الرَّحِيمِ',
            transliteration: 'Ar-rahmani ar-raheem',
            translation: 'الرحمن الرحيم',
            audioPath: 'assets/audio/quran/fatiha_3.mp3',
          ),
          QuranVerse(
            number: 4,
            arabicText: 'مَالِكِ يَوْمِ الدِّينِ',
            transliteration: 'Maliki yawmi ad-deen',
            translation: 'مالك يوم الدين',
            audioPath: 'assets/audio/quran/fatiha_4.mp3',
          ),
          QuranVerse(
            number: 5,
            arabicText: 'إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ',
            transliteration: 'Iyyaka na\'budu wa iyyaka nasta\'een',
            translation: 'إياك نعبد وإياك نستعين',
            audioPath: 'assets/audio/quran/fatiha_5.mp3',
          ),
          QuranVerse(
            number: 6,
            arabicText: 'اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ',
            transliteration: 'Ihdina as-sirata al-mustaqeem',
            translation: 'اهدنا الصراط المستقيم',
            audioPath: 'assets/audio/quran/fatiha_6.mp3',
          ),
          QuranVerse(
            number: 7,
            arabicText: 'صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ وَلَا الضَّالِّينَ',
            transliteration: 'Sirata allatheena an\'amta alayhim ghayri al-maghdoobi alayhim wala ad-dalleen',
            translation: 'صراط الذين أنعمت عليهم غير المغضوب عليهم ولا الضالين',
            audioPath: 'assets/audio/quran/fatiha_7.mp3',
          ),
        ],
        audioPath: 'assets/audio/quran/fatiha_full.mp3',
        description: 'سورة الفاتحة - أم الكتاب وأساس الصلاة',
        difficulty: DifficultyLevel.beginner,
        category: 'السور الأساسية',
      ),
      QuranLesson(
        id: 'ikhlas',
        surahName: 'Al-Ikhlas',
        surahNameArabic: 'الإخلاص',
        surahNumber: 112,
        verses: [
          QuranVerse(
            number: 1,
            arabicText: 'قُلْ هُوَ اللَّهُ أَحَدٌ',
            transliteration: 'Qul huwa Allahu ahad',
            translation: 'قل هو الله أحد',
            audioPath: 'assets/audio/quran/ikhlas_1.mp3',
          ),
          QuranVerse(
            number: 2,
            arabicText: 'اللَّهُ الصَّمَدُ',
            transliteration: 'Allahu as-samad',
            translation: 'الله الصمد',
            audioPath: 'assets/audio/quran/ikhlas_2.mp3',
          ),
          QuranVerse(
            number: 3,
            arabicText: 'لَمْ يَلِدْ وَلَمْ يُولَدْ',
            transliteration: 'Lam yalid wa lam yoolad',
            translation: 'لم يلد ولم يولد',
            audioPath: 'assets/audio/quran/ikhlas_3.mp3',
          ),
          QuranVerse(
            number: 4,
            arabicText: 'وَلَمْ يَكُن لَّهُ كُفُوًا أَحَدٌ',
            transliteration: 'Wa lam yakun lahu kufuwan ahad',
            translation: 'ولم يكن له كفواً أحد',
            audioPath: 'assets/audio/quran/ikhlas_4.mp3',
          ),
        ],
        audioPath: 'assets/audio/quran/ikhlas_full.mp3',
        description: 'سورة الإخلاص - تعادل ثلث القرآن',
        difficulty: DifficultyLevel.beginner,
        category: 'السور القصيرة',
      ),
      QuranLesson(
        id: 'falaq',
        surahName: 'Al-Falaq',
        surahNameArabic: 'الفلق',
        surahNumber: 113,
        verses: [
          QuranVerse(
            number: 1,
            arabicText: 'قُلْ أَعُوذُ بِرَبِّ الْفَلَقِ',
            transliteration: 'Qul a\'oothu bi rabbi al-falaq',
            translation: 'قل أعوذ برب الفلق',
            audioPath: 'assets/audio/quran/falaq_1.mp3',
          ),
          QuranVerse(
            number: 2,
            arabicText: 'مِن شَرِّ مَا خَلَقَ',
            transliteration: 'Min sharri ma khalaq',
            translation: 'من شر ما خلق',
            audioPath: 'assets/audio/quran/falaq_2.mp3',
          ),
          QuranVerse(
            number: 3,
            arabicText: 'وَمِن شَرِّ غَاسِقٍ إِذَا وَقَبَ',
            transliteration: 'Wa min sharri ghasiqin itha waqab',
            translation: 'ومن شر غاسق إذا وقب',
            audioPath: 'assets/audio/quran/falaq_3.mp3',
          ),
          QuranVerse(
            number: 4,
            arabicText: 'وَمِن شَرِّ النَّفَّاثَاتِ فِي الْعُقَدِ',
            transliteration: 'Wa min sharri an-naffathati fi al-uqad',
            translation: 'ومن شر النفاثات في العقد',
            audioPath: 'assets/audio/quran/falaq_4.mp3',
          ),
          QuranVerse(
            number: 5,
            arabicText: 'وَمِن شَرِّ حَاسِدٍ إِذَا حَسَدَ',
            transliteration: 'Wa min sharri hasidin itha hasad',
            translation: 'ومن شر حاسد إذا حسد',
            audioPath: 'assets/audio/quran/falaq_5.mp3',
          ),
        ],
        audioPath: 'assets/audio/quran/falaq_full.mp3',
        description: 'سورة الفلق - المعوذة الأولى',
        difficulty: DifficultyLevel.beginner,
        category: 'المعوذتان',
      ),
    ];
  }
}

class QuranVerse {
  final int number;
  final String arabicText;
  final String transliteration;
  final String translation;
  final String audioPath;

  QuranVerse({
    required this.number,
    required this.arabicText,
    required this.transliteration,
    required this.translation,
    required this.audioPath,
  });
}

enum DifficultyLevel {
  beginner,
  intermediate,
  advanced,
}

extension DifficultyLevelExtension on DifficultyLevel {
  String get arabicName {
    switch (this) {
      case DifficultyLevel.beginner:
        return 'مبتدئ';
      case DifficultyLevel.intermediate:
        return 'متوسط';
      case DifficultyLevel.advanced:
        return 'متقدم';
    }
  }

  Color get color {
    switch (this) {
      case DifficultyLevel.beginner:
        return Colors.green;
      case DifficultyLevel.intermediate:
        return Colors.orange;
      case DifficultyLevel.advanced:
        return Colors.red;
    }
  }
}
