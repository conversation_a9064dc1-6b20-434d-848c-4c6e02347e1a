import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../utils/constants.dart';
import '../widgets/animated_character.dart';
import 'educational_videos_screen.dart';
import 'nearby_doctors_screen.dart';
import 'articles_screen.dart';

class ParentsEducationScreen extends StatefulWidget {
  const ParentsEducationScreen({super.key});

  @override
  State<ParentsEducationScreen> createState() => _ParentsEducationScreenState();
}

class _ParentsEducationScreenState extends State<ParentsEducationScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text(
            'دليل الآباء التعليمي',
            style: TextStyle(
              color: AppConstants.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          centerTitle: true,
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios, color: AppConstants.primaryColor),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: Container(
          decoration: const BoxDecoration(
            gradient: AppConstants.backgroundGradient,
          ),
          child: SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.paddingLarge),
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: Column(
                  children: [
                    // Header Section
                    _buildHeader(),
                    
                    const SizedBox(height: AppConstants.paddingXLarge),
                    
                    // Animated Character
                    // _buildAnimatedCharacter(),
                    
                    const SizedBox(height: AppConstants.paddingXLarge),
                    
                    // Education Cards
                    _buildEducationCards(),
                    
                    const SizedBox(height: AppConstants.paddingLarge),
                    
                    // Tips Section
                    _buildTipsSection(),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.school,
            size: 60,
            color: AppConstants.primaryColor,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'مرحباً بك في دليل الآباء',
            style: AppConstants.headingStyle.copyWith(
              color: AppConstants.primaryColor,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'تعلم المزيد عن حالة طفلك وكيفية دعمه في رحلة علاج النطق',
            style: AppConstants.bodyStyle.copyWith(
              color: AppConstants.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedCharacter() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.8),
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: const Center(
        child: AnimatedCharacter(
          size: 150,
          message: 'مرحباً! أنا هنا لمساعدتكم',
        ),
      ),
    );
  }

  Widget _buildEducationCards() {
    final educationOptions = [
      {
        'title': 'فيديوهات تعليمية',
        'subtitle': 'شاهد فيديوهات من الخبراء',
        'icon': Icons.play_circle_filled,
        'color': const Color(0xFF2196F3),
        'gradient': [const Color(0xFF2196F3), const Color(0xFF42A5F5)],
        'emoji': '🎥',
        'onTap': () => _navigateToScreen(const EducationalVideosScreen()),
      },
      {
        'title': 'الأطباء القريبون',
        'subtitle': 'ابحث عن أخصائيين قريبين منك',
        'icon': Icons.location_on,
        'color': const Color(0xFF4CAF50),
        'gradient': [const Color(0xFF4CAF50), const Color(0xFF66BB6A)],
        'emoji': '📍',
        'onTap': () => _navigateToScreen(const NearbyDoctorsScreen()),
      },
      {
        'title': 'مقالات تعليمية',
        'subtitle': 'اقرأ نصائح وإرشادات الخبراء',
        'icon': Icons.article,
        'color': const Color(0xFFFF9800),
        'gradient': [const Color(0xFFFF9800), const Color(0xFFFFB74D)],
        'emoji': '📚',
        'onTap': () => _navigateToScreen(const ArticlesScreen()),
      },
    ];

    return AnimationLimiter(
      child: Column(
        children: educationOptions.asMap().entries.map((entry) {
          final index = entry.key;
          final option = entry.value;
          
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 600),
            child: SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(
                child: Container(
                  margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
                  child: _buildEducationCard(option),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildEducationCard(Map<String, dynamic> option) {
    return GestureDetector(
      onTap: option['onTap'],
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: option['gradient'],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          boxShadow: [
            BoxShadow(
              color: option['color'].withOpacity(0.3),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
              ),
              child: Text(
                option['emoji'],
                style: const TextStyle(fontSize: 30),
              ),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    option['title'],
                    style: AppConstants.subHeadingStyle.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppConstants.paddingSmall),
                  Text(
                    option['subtitle'],
                    style: AppConstants.bodyStyle.copyWith(
                      color: Colors.white.withOpacity(0.9),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.white,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTipsSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.lightbulb,
                color: AppConstants.accentColor,
                size: 30,
              ),
              const SizedBox(width: AppConstants.paddingSmall),
              Text(
                'نصائح سريعة',
                style: AppConstants.subHeadingStyle.copyWith(
                  color: AppConstants.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          _buildTipItem('💬', 'تحدث مع طفلك بانتظام واستمع إليه بصبر'),
          _buildTipItem('📖', 'اقرأ له القصص يومياً لتطوير مهاراته اللغوية'),
          _buildTipItem('🎯', 'مارس التمارين المنزلية بانتظام'),
          _buildTipItem('👏', 'احتفل بالإنجازات الصغيرة وشجعه دائماً'),
        ],
      ),
    );
  }

  Widget _buildTipItem(String emoji, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingSmall),
      child: Row(
        children: [
          Text(emoji, style: const TextStyle(fontSize: 20)),
          const SizedBox(width: AppConstants.paddingSmall),
          Expanded(
            child: Text(
              text,
              style: AppConstants.bodyStyle.copyWith(
                color: AppConstants.textPrimaryColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToScreen(Widget screen) {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => screen,
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.ease;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
      ),
    );
  }
}
