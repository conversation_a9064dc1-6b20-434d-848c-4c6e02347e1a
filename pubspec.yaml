name: not9k_mohim
description: "نطقك مهم - تطبيق علاج النطق للأطفال"
publish_to: 'none'
version: 1.0.0

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: any
  # Audio recording and playback
  record: ^5.0.4
  audioplayers: ^6.0.0
  permission_handler: ^11.3.1
  # Speech recognition - removed due to compatibility issues
  # speech_to_text: ^6.6.2
  # Animations
  lottie: ^3.1.2
  # State management
  provider: ^6.1.2
  # Local storage
  shared_preferences: ^2.2.2
  # UI components
  flutter_staggered_animations: ^1.1.1
  # URL launcher for contact
  url_launcher: ^6.2.5
  # File handling
  path_provider: ^2.1.2
  # Video player for educational content
  video_player: ^2.8.6
  youtube_player_flutter: ^9.0.3
  # Location services
  geolocator: ^12.0.0
  http: ^1.1.0
  flutter_chat_ui: ^1.6.8

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.13.1

flutter:
  uses-material-design: true

  # Arabic fonts
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600

  # Assets
  assets:
    - assets/images/
    - assets/audio/
    - assets/videos/
    - assets/animations/
    - assets/fonts/

# Flutter Launcher Icons Configuration
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/logo.jpg"
  min_sdk_android: 21 # android min sdk min:16, default 21
  remove_alpha_ios: true
  web:
    generate: true
    image_path: "assets/images/icon.jpeg"
    background_color: "#4CAF50"
    theme_color: "#4CAF50"
  windows:
    generate: true
    image_path: "assets/images/icon.jpeg"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/images/icon.jpeg"
