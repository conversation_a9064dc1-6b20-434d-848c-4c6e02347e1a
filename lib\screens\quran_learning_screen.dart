import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:audioplayers/audioplayers.dart';
import '../models/quran_lesson.dart';
import '../utils/constants.dart';
import '../utils/arabic_text.dart';
import '../widgets/animated_character.dart';
import 'quran_lesson_detail_screen.dart';

class QuranLearningScreen extends StatefulWidget {
  const QuranLearningScreen({super.key});

  @override
  State<QuranLearningScreen> createState() => _QuranLearningScreenState();
}

class _QuranLearningScreenState extends State<QuranLearningScreen> {
  final AudioPlayer _audioPlayer = AudioPlayer();
  List<QuranLesson> _lessons = [];
  List<QuranLesson> _filteredLessons = [];
  String _selectedCategory = 'الكل';
  DifficultyLevel? _selectedDifficulty;

  @override
  void initState() {
    super.initState();
    _lessons = QuranLesson.getAllLessons();
    _filteredLessons = _lessons;
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
      appBar: AppBar(
        title: const Text('تعلم القرآن الكريم'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppConstants.backgroundGradient,
        ),
        child: Column(
          children: [
            // Character and welcome message
            _buildHeader(),
            
            // Filter chips
            _buildFilterChips(),
            
            // Lessons list
            Expanded(
              child: _buildLessonsList(),
            ),
          ],
        ),
      ),
    ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        children: [
          const AnimatedCharacter(
            size: 200,
            message: '🌟 مرحباً بك في رحلة تعلم القرآن الكريم 📖',
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            '🎯 تعلم تلاوة القرآن الكريم بطريقة تفاعلية وممتعة 🎵',
            style: AppConstants.subHeadingStyle,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    final categories = ['الكل', 'السور الأساسية', 'السور القصيرة', 'المعوذتان'];
    
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = _selectedCategory == category;
          
          return Container(
            margin: const EdgeInsets.only(right: AppConstants.paddingSmall),
            child: FilterChip(
              label: Text(category),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedCategory = category;
                  _filterLessons();
                });
              },
              backgroundColor: Colors.white,
              selectedColor: AppConstants.primaryColor.withValues(alpha: 0.2),
              checkmarkColor: AppConstants.primaryColor,
            ),
          );
        },
      ),
    );
  }

  Widget _buildLessonsList() {
    if (_filteredLessons.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.menu_book,
              size: 64,
              color: AppConstants.textSecondaryColor,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              'لا توجد دروس متاحة',
              style: AppConstants.subHeadingStyle.copyWith(
                color: AppConstants.textSecondaryColor,
              ),
            ),
          ],
        ),
      );
    }

    return AnimationLimiter(
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        itemCount: _filteredLessons.length,
        itemBuilder: (context, index) {
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: AppConstants.animationMedium,
            child: SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(
                child: _buildLessonCard(_filteredLessons[index]),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildLessonCard(QuranLesson lesson) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: Card(
        child: InkWell(
          onTap: () => _navigateToLessonDetail(lesson),
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          child: Container(
            padding: const EdgeInsets.all(AppConstants.paddingLarge),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with surah name and number
                Row(
                  children: [
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        gradient: AppConstants.primaryGradient,
                        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                      ),
                      child: Center(
                        child: Text(
                          lesson.surahNumber.toString(),
                          style: AppConstants.subHeadingStyle.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: AppConstants.paddingMedium),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            lesson.surahNameArabic,
                            style: AppConstants.subHeadingStyle,
                          ),
                          const SizedBox(height: AppConstants.paddingSmall),
                          Text(
                            lesson.surahName,
                            style: AppConstants.captionStyle,
                          ),
                        ],
                      ),
                    ),
                    // Difficulty badge
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppConstants.paddingSmall,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: lesson.difficulty.color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                        border: Border.all(color: lesson.difficulty.color),
                      ),
                      child: Text(
                        lesson.difficulty.arabicName,
                        style: AppConstants.captionStyle.copyWith(
                          color: lesson.difficulty.color,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: AppConstants.paddingMedium),
                
                // Description
                Text(
                  lesson.description,
                  style: AppConstants.bodyStyle,
                ),
                
                const SizedBox(height: AppConstants.paddingMedium),
                
                // Verses count and category
                Row(
                  children: [
                    Icon(
                      Icons.format_list_numbered,
                      size: 16,
                      color: AppConstants.textSecondaryColor,
                    ),
                    const SizedBox(width: AppConstants.paddingSmall),
                    Text(
                      '📖 ${lesson.verses.length} آيات',
                      style: AppConstants.captionStyle,
                    ),
                    const SizedBox(width: AppConstants.paddingMedium),
                    Icon(
                      Icons.category,
                      size: 16,
                      color: AppConstants.textSecondaryColor,
                    ),
                    const SizedBox(width: AppConstants.paddingSmall),
                    Text(
                      '🏷️ ${lesson.category}',
                      style: AppConstants.captionStyle,
                    ),
                  ],
                ),
                
                const SizedBox(height: AppConstants.paddingMedium),
                
                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _playFullSurah(lesson),
                        icon: const Icon(Icons.volume_up, size: 18),
                        label: const Text('🎵 استمع للسورة'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppConstants.secondaryColor,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                    const SizedBox(width: AppConstants.paddingMedium),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _navigateToLessonDetail(lesson),
                        icon: const Icon(Icons.play_circle_filled, size: 18),
                        label: const Text('📚 ابدأ التعلم'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppConstants.primaryColor,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _filterLessons() {
    setState(() {
      _filteredLessons = _lessons.where((lesson) {
        final categoryMatch = _selectedCategory == 'الكل' || lesson.category == _selectedCategory;
        final difficultyMatch = _selectedDifficulty == null || lesson.difficulty == _selectedDifficulty;
        return categoryMatch && difficultyMatch;
      }).toList();
    });
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصفية الدروس'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('مستوى الصعوبة:'),
            const SizedBox(height: AppConstants.paddingSmall),
            Wrap(
              spacing: AppConstants.paddingSmall,
              children: [
                FilterChip(
                  label: const Text('الكل'),
                  selected: _selectedDifficulty == null,
                  onSelected: (selected) {
                    setState(() {
                      _selectedDifficulty = null;
                      _filterLessons();
                    });
                    Navigator.pop(context);
                  },
                ),
                ...DifficultyLevel.values.map((difficulty) => FilterChip(
                  label: Text(difficulty.arabicName),
                  selected: _selectedDifficulty == difficulty,
                  onSelected: (selected) {
                    setState(() {
                      _selectedDifficulty = selected ? difficulty : null;
                      _filterLessons();
                    });
                    Navigator.pop(context);
                  },
                )),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _playFullSurah(QuranLesson lesson) async {
    try {
      // In a real app, you would play the actual audio file
      // await _audioPlayer.play(AssetSource(lesson.audioPath));
      
      // Simulate audio playback
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تشغيل ${lesson.surahNameArabic}...'),
            backgroundColor: AppConstants.primaryColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('خطأ في تشغيل الصوت'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  void _navigateToLessonDetail(QuranLesson lesson) {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => 
            QuranLessonDetailScreen(lesson: lesson),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.ease;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
      ),
    );
  }
}
