# نُطقك مهم - تطبيق علاج النطق للأطفال

تطبيق Flutter مخصص لمساعدة الأطفال الذين يعانون من اضطرابات النطق واللغة.

## الميزات الرئيسية

### 🎯 التشخيص
- تشخيص ثلاثة أنواع من اضطرابات النطق:
  - التلعثم
  - اللثغة
  - البكم الانتقائي
- شرح تفصيلي لكل اضطراب مع الصوت والنص
- عرض الأعراض وكلمات التدريب

### 🎤 العلاج التفاعلي
- تسجيل الصوت ومقارنته بالنطق الصحيح
- نظام تقييم بصري (✅ صحيح / ❌ خطأ)
- شخصية كرتونية متحركة تقدم التشجيع
- كلمات تدريب مخصصة لكل اضطراب
- تتبع التقدم والإحصائيات

### 👨‍⚕️ الاستشارة المهنية
- دليل أخصائيي علاج النطق
- معلومات تفصيلية (الاسم، التخصص، الهاتف، الموقع، ساعات العمل)
- حجز المواعيد مباشرة من التطبيق
- إمكانية الاتصال وإرسال الإيميل

### 📖 تعلم القرآن الكريم
- دروس تفاعلية لتعلم تلاوة القرآن
- تحسين النطق من خلال التلاوة
- واجهة مناسبة للأطفال
- تتبع التقدم في التعلم

### 👨‍👩‍👧‍👦 دليل الآباء التعليمي (جديد!)
- **فيديوهات تعليمية**: مكتبة شاملة من الفيديوهات التعليمية من الخبراء
  - فهم اضطرابات النطق عند الأطفال
  - تمارين النطق المنزلية
  - دور الأسرة في العلاج
  - ألعاب تطوير النطق
- **الأطباء القريبون**: دليل تفاعلي للأطباء والأخصائيين
  - البحث حسب الموقع والتخصص
  - معلومات تفصيلية عن كل طبيب
  - إمكانية الاتصال المباشر
  - فتح الخرائط للوصول للعيادة
- **مقالات تعليمية**: مكتبة من المقالات المفيدة
  - نصائح الخبراء
  - قصص نجاح
  - إرشادات التمارين المنزلية
  - التشخيص المبكر

### 🎨 التصميم
- واجهة مستخدم باللغة العربية بالكامل
- دعم RTL (من اليمين إلى اليسار)
- شخصية كرتونية متحركة
- ألوان وتصميم مناسب للأطفال
- انتقالات سلسة وتأثيرات بصرية

## التقنيات المستخدمة

- **Flutter** - إطار العمل الأساسي
- **Dart** - لغة البرمجة
- **record** - تسجيل الصوت
- **audioplayers** - تشغيل الملفات الصوتية
- **permission_handler** - إدارة الأذونات
- **url_launcher** - فتح الروابط والاتصال
- **provider** - إدارة الحالة
- **flutter_staggered_animations** - الرسوم المتحركة
- **youtube_player_flutter** - تشغيل فيديوهات YouTube
- **geolocator** - خدمات تحديد الموقع
- **lottie** - الرسوم المتحركة المتقدمة

## البنية التقنية

```
lib/
├── main.dart                 # نقطة البداية
├── models/                   # نماذج البيانات
│   ├── speech_disorder.dart  # نموذج اضطرابات النطق
│   ├── specialist.dart       # نموذج الأخصائيين
│   ├── treatment_session.dart # نموذج جلسات العلاج
│   ├── educational_video.dart # نموذج الفيديوهات التعليمية
│   ├── doctor.dart           # نموذج الأطباء القريبين
│   └── educational_article.dart # نموذج المقالات التعليمية
├── screens/                  # الشاشات الرئيسية
│   ├── home_screen.dart      # الشاشة الرئيسية
│   ├── diagnosis_screen.dart # شاشة التشخيص
│   ├── treatment_screen.dart # شاشة العلاج
│   └── specialists_screen.dart # شاشة الأخصائيين
├── widgets/                  # المكونات المخصصة
│   ├── animated_character.dart # الشخصية المتحركة
│   ├── audio_recorder.dart   # مسجل الصوت
│   ├── pronunciation_feedback.dart # تقييم النطق
│   └── specialist_card.dart  # بطاقة الأخصائي
├── services/                 # الخدمات
├── utils/                    # الأدوات المساعدة
│   ├── constants.dart        # الثوابت
│   └── arabic_text.dart      # النصوص العربية
└── assets/                   # الملفات الثابتة
    ├── images/               # الصور
    ├── audio/                # الملفات الصوتية
    ├── animations/           # الرسوم المتحركة
    └── fonts/                # الخطوط العربية
```

## التشغيل

1. تأكد من تثبيت Flutter SDK
2. استنساخ المشروع
3. تشغيل الأوامر التالية:

```bash
flutter pub get
flutter run
```

## الأذونات المطلوبة

- **الميكروفون**: لتسجيل الصوت
- **التخزين**: لحفظ التسجيلات
- **الإنترنت**: للاتصال بالأخصائيين

## المتطلبات

- Flutter SDK 3.8.1+
- Dart 3.0+
- Android API level 21+ / iOS 11.0+

## الرسالة التحفيزية

> "صوتك مرآة فكرك، وكلماتك جسور تواصلك، إمنح لنطقك رونقه، ولحديثك بهاءه، وإبدأ رحلة التعبير بكل ثقة"

## المساهمة

نرحب بالمساهمات لتطوير التطبيق وإضافة ميزات جديدة.

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والتطويري.
