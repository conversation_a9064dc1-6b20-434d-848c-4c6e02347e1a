import 'package:flutter/material.dart';
import '../widgets/ai_chatbot.dart';

class AIAssistantScreen extends StatefulWidget {
  const AIAssistantScreen({Key? key}) : super(key: key);

  @override
  State<AIAssistantScreen> createState() => _AIAssistantScreenState();
}

class _AIAssistantScreenState extends State<AIAssistantScreen> {
  bool _showWelcome = true;

  void _onFirstUserMessage() {
    setState(() {
      _showWelcome = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: const Color(0xFFF3E8FF),
        body: SafeArea(
          minimum: EdgeInsets.zero,
          child: Column(
            children: [
              // Header with robot icon and title (no extra padding)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.only(top: 8, bottom: 8),
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Color(0xFF7C3AED), Color(0xFF818CF8)],
                    begin: Alignment.topRight,
                    end: Alignment.bottomLeft,
                  ),
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(32),
                    bottomRight: Radius.circular(32),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Color(0x337C3AED),
                      blurRadius: 16,
                      offset: Offset(0, 8),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircleAvatar(
                      radius: 28,
                      backgroundColor: Colors.white,
                      child: Icon(
                        Icons.smart_toy_rounded,
                        color: Color(0xFF7C3AED),
                        size: 36,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'مساعد فصيح الذكي',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        letterSpacing: 1.2,
                      ),
                    ),
                  ],
                ),
              ),
              // Welcome message (only before chat starts)
              if (_showWelcome)
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20.0,
                    vertical: 12,
                  ),
                  child: Card(
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(24),
                    ),
                    color: Colors.white,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 18,
                        horizontal: 18,
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Icon(
                            Icons.info_outline,
                            color: Color(0xFF7C3AED),
                            size: 28,
                          ),
                          const SizedBox(width: 10),
                          Expanded(
                            child: Text(
                              'مرحباً بك في مساعد فصيح الذكي!\n\nأنا هنا لمساعدتك في كل ما يتعلق باضطرابات النطق عند الأطفال: التشخيص، التمارين، الدعم النفسي، ونصائح للأهل. اسألني عن أي شيء يخص علاج النطق أو كيفية دعم طفلك في رحلة التعبير بثقة.',
                              style: const TextStyle(
                                fontSize: 15,
                                color: Color(0xFF4B5563),
                                fontWeight: FontWeight.w500,
                                height: 1.5,
                              ),
                              textAlign: TextAlign.right,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              // Chat Area (take all remaining space)
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 4.0,
                    vertical: 4,
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(32),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.deepPurple.withOpacity(0.08),
                          blurRadius: 16,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: AIChatBot(onFirstUserMessage: _onFirstUserMessage),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
